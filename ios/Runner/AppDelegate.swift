//
//  AppDelegate 2.swift
//  Runner
//
//  Created by 张仕鹏 on 2025/7/1.
//


import Flutter
//import RingSDK
//#import <RingSDK/RingSDK.h>
//import <RingSDK.h>
import UIKit
import flutter_local_notifications
import AHDevicePlugin


class AojSyncDelegate: NSObject, AHDeviceDataDelegate {
    static let shared = AojSyncDelegate()
    private override init() {}

    func bleDevice(_ device: BTDeviceInfo!, didConnectStateChanged state: BTConnectState) {
        // 处理连接状态变化
        print("Device \(device.macAddress ?? "") state changed: \(state.rawValue)")
        // 你可以通过 EventChannel 推送到 Flutter
    }

    func bleDevice(_ device: BTDeviceInfo!, didDataUpdateNotification obj: BTDeviceData!) {
        // 处理设备数据更新
        print("Device \(device.macAddress ?? "") data updated: \(String(describing: obj))")
    }
}


@main
@objc class AppDelegate: FlutterAppDelegate {
    
    //    // 戒指服务入口
    //    let service = BTService.shared()
    // 戒指服务入口
    var service: BTService?
    var aojDevice:BTDeviceInfo = BTDeviceInfo.init();
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        
        let versionName : String?=AHDevicePlugin.default()?.versionName;
         print("version name:\(versionName ?? "default Name")");
         
        //init
        AHDevicePlugin.default().initPlugin(withDispatch: DispatchQueue.init(label: "bluetoothQueue"));
        print("aiRing插件初始化成功")
        
        
        
        // 2. 安排初始后台任务
        FlutterLocalNotificationsPlugin.setPluginRegistrantCallback { (registry) in
            GeneratedPluginRegistrant.register(with: registry)
        }
     
        if #available(iOS 10.0, *) {
            UNUserNotificationCenter.current().delegate = self as UNUserNotificationCenterDelegate
        }
        GeneratedPluginRegistrant.register(with: self)
        
        
        
        
        let controller: FlutterViewController = window?.rootViewController as! FlutterViewController
        // 创建第一个通道，用于单次事件
        let singleTimeChannel = FlutterMethodChannel(name: "com.aihnet.aicare_single_time", binaryMessenger: controller.binaryMessenger)
        
        
        
        singleTimeChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) in
            switch call.method {
            case "init":
                    self.aizoInit(result: result)
            case "aizoConnect":
                // 获取传递的参数
                if let args = call.arguments as? [String: Any],
                   let deviceMac = args["deviceMac"] as? String {
                    self.aizoConnect(result: result,deviceMac: deviceMac)
                } else {
                    // 参数无效时返回错误信息
                    result(FlutterError(code: "INVALID_ARGUMENT", message: "Invalid argument", details: nil))
                }
            case "aizoGetSleepData":
                // 获取传递的时间戳
                    guard let args = call.arguments as? [String: Any],
                          let timestamp = args["time"] as? Double else {
                        result(FlutterError(code: "INVALID_ARGUMENT",
                                          message: "必须提供时间参数",
                                          details: nil))
                        return
                    }
                    
                    // 转换为DateComponents
                    let date = Date(timeIntervalSince1970: timestamp / 1000)
                    let calendar = Calendar.current
                    let dateComponents = calendar.dateComponents([.year, .month, .day], from: date)
                    print("dateComponents")
                    print(dateComponents)
                    
                    // 调用获取睡眠数据方法
                    self.fetchSleepData(for: dateComponents, result: result)
                
            case "aizoUnbind":
                do {
                    // 执行解绑命令
                    try self.service!.baseReq.execute(.unbind) {}
                    
                    // 尝试解绑设备
                    self.service!.unbindDevice()
                    
                    // 如果解绑成功，返回结果
                    result(true)
                } catch let error as NSError {
                    // 捕获并处理错误，将错误信息传递给 Flutter
                    result(FlutterError(code: "UNBIND_ERROR",
                                        message: "Failed to unbind device",
                                        details: error.localizedDescription))
                } catch {
                    // 处理其他未知错误，将错误信息传递给 Flutter
                    result(FlutterError(code: "UNEXPECTED_ERROR",
                                        message: "An unexpected error occurred",
                                        details: error.localizedDescription))
                }
            case "aizoInstantMeasurement":
                // 获取传递的参数
                if let args = call.arguments as? [String: Any],
                   let type = args["type"] as? Int{
                    var cmd: DeviceCommand;
                    switch(type){
                    case 0:
                        return cmd = DeviceCommand.measureHr;
                    case 1:
                        return cmd = DeviceCommand.measureSpo2;
                    case 2:
                        return cmd = DeviceCommand.measureTemp;
                    default:
                        return;
                    }
                    
                    // 根据传入的参数进行处理
                    let response = "Received type: \(type), cmd: \(cmd)"
                    self.aizoInstantMeasurement(result: result,cmd: cmd)
                } else {
                    // 参数无效时返回错误信息
                    result(FlutterError(code: "INVALID_ARGUMENT", message: "Invalid argument", details: nil))
                }
            case "aizoGetHardwareData":
                // 假设 `self.service!.baseReq.fetch` 的闭包中包含 `FlutterResult` 类型的 `result` 参数（Flutter 方法通道的回调）
                self.service!.baseReq.fetch(.information) { obj, type in
                    guard let model = obj as? BTAboutModel else {
                        // 若模型为空，通过 `result` 返回错误
                        result(FlutterError(code: "model_nil", message: "BTAboutModel is nil", details: nil));
                        return;
                    }
                    let jsonStr = model.toJSONString();
                    // 通过 `result` 将数据传回 Flutter
                    result(jsonStr);
                };
                break;
            
            case "aizoGetHealthData":
                // 获取传递的参数
            
                if let args = call.arguments as? [String: Any],
                   let time = args["time"] as? String{
                    // 将传递的时间字符串解析为 DateComponents
                    guard let dateComponents = self.parseDateStringToComponents(dateString: time) else {
                        result(FlutterError(code: "INVALID_DATE", message: "Invalid date format. Expected format: yyyy-MM-dd", details: nil))
                        return
                    }
                    
                    // 确保 service 不为空
                    guard let service = self.service else {
                        result(FlutterError(code: "SERVICE_NOT_INITIALIZED", message: "Service is not initialized", details: nil))
                        return
                    }
                    
                    // 根据日期获取健康数据
                    service.healthReq.fetchData(with: dateComponents) { models in
                        
                        // 检查数据是否为空
                        if models.isEmpty {
                            print("数据为空")
                            result([])
                            return
                        }
                        
                        // 继续处理数据
                        let jsonStr1 = BTHealthModel.toJSONString(with: models)
                        let jsonStr2 = self.healthDataToJSON(with: models)
                        print("原方法获得的数据")
                        print(jsonStr1)
                        print("转换后的数据")
                        print(jsonStr2)
                        result(jsonStr2)
                    }
                } else {
                    // 参数无效时返回错误信息
                    result(FlutterError(code: "INVALID_ARGUMENT", message: "Invalid argument", details: nil))
                }
            case "aizoSetMeasureInterval":
                // 获取传递的参数
                if let args = call.arguments as? [String: Any],
                   let time = args["time"] as? Int{
                    
                    // 根据日期获取健康数据
                    self.service!.baseReq.setDeviceMeasureTime(time) { isSuccess in
                        if isSuccess {
                            result(true)
                        } else {
                            result(FlutterError(code: "INVALID_ARGUMENT", message: "Failed to Set Interval.", details: nil))
                        }
                    }
                } else {
                    // 参数无效时返回错误信息
                    result(FlutterError(code: "INVALID_ARGUMENT", message: "Invalid argument", details: nil))
                }
            case "aizoGetMeasureInterval":
                // 先判断 service 是否为空，为空不执行
                guard let service = self.service else {
                    result(FlutterError(code: "SERVICE_NULL", message: "Service is null", details: nil))
                    return
                }
                self.service!.baseReq.queryMeasurementInterval { isSuccess, intervalModel in

                    if isSuccess, let interval = intervalModel {
                        let response: [String: Any] = [
                            "intervalList": interval.supportedIntervals, // 直接传数组
                            "currentInterval": interval.currentInterval,
                            "defaultInterval": interval.defaultInterval
                        ]
                        result(response)
                    } else {
                        result(FlutterError(code: "INVALID_ARGUMENT", message: "Failed to Get Interval.", details: nil))
                    }
                }
            case "aizoConfigurationList":
                // 1. 安全解包 self.service
                guard let service = self.service else {
                    result(nil)  // 如果 service 为空，直接返回 nil
                    return
                }
                
                if service.isConnected {
                    service.baseReq.fetch(.features) { obj, type in
                        guard let model = obj as? BTFeatureModel else {
                            result(nil)  // 如果获取失败，返回 nil
                            return
                        }
                        
                        // 将 BTFeatureModel 转换为与 Android 端一致的格式
                        let config: [String: Any] = [
                            "bloodOxygenMonitoring": model.bloodOxygenMeasurement,  // 移除 ??
                            "bloodPressureMonitoring": false,
                            "bloodSugarMonitoring": false,
                            "breatheMonitoring": false,
                            "ecgMonitoring": model.ecgMeasurement,  // 移除 ??
                            "heartRateMonitoring": model.hrMeasurement,  // 移除 ??
                            "isHeartRateSupport": true,
                            "isTouchSet": model.isSupportTouchSwitch,  // 移除 ??
                            "pressureMonitoring": false,
                            "sleepMonitoring": model.sleepMeasurement,  // 移除 ??
                            "sosTriggerMode": model.sosTriggerMode,  // 移除 ??
                            "supportSos": model.isSupportSOS,  // 移除 ??
                            "supportWakeupByGesture": model.isSupportGestureWakeup,  // 移除 ??
                            "temperatureMonitoring": model.bodyTempMeasurement  // 移除 ??
                        ]
                        
                        do {
                            let jsonData = try JSONSerialization.data(withJSONObject: config)
                            if let jsonString = String(data: jsonData, encoding: .utf8) {
                                result(jsonString)
                            } else {
                                result(nil)
                            }
                        } catch {
                            result(nil)
                        }
                    }
                } else {
                    result(nil)  // 设备未连接，返回 nil
                }
            case "calculateHealthScore":
                self.calculateHealthScore(call,result:result)
                
            case "getConnect":
                // 1. 安全解包 self.service
                guard let service = self.service else {
                    result("null") // 或返回明确的错误标识
                    return
                }

                // 2. 检查连接状态
                if service.isConnected {
                    // 3. 安全获取配对设备
                    guard let devices = service.getPairedDevice() else {
                        result("null") // 设备获取失败
                        return
                    }
                    
                    // 4. 安全获取 macAddress
                    if !devices.macAddress.isEmpty {
                        result(devices.macAddress)
                    } else {
                        result("null")
                    }
                } else {
                    result("null") // 或返回 "null"
                }
            default:
                result(FlutterMethodNotImplemented)
            }
        }
        
        
        // 创建第二个通道，用于电池状态持续监听
        let batteryStatusChannel = FlutterEventChannel(name: "com.aihnet.aicare_manyTimesBatteryStatus", binaryMessenger: controller.binaryMessenger)
        
        batteryStatusChannel.setStreamHandler(BatteryStatusStream())
        
        // 创建第三个通道，用于aizo扫描持续监听
        let scanningStatusChannel = FlutterEventChannel(name: "com.aihnet.aicare_manyTimesScanningStatus", binaryMessenger: controller.binaryMessenger)
        
        scanningStatusChannel.setStreamHandler(ScanningStatusStream())
        
        // 创建第四个通道，用于aizo监听状态变化
        let ringStatusChannel = FlutterEventChannel(name: "com.aihnet.aicare_manyTimesRingStatus", binaryMessenger: controller.binaryMessenger)
        
        ringStatusChannel.setStreamHandler(RingStatusStream())
        
        // 创建第五个通道，用于流式传输测量结果
        let instantMeasurementChannel = FlutterEventChannel(name: "com.aihnet.aicare_manyTimesInstantMeasurement", binaryMessenger: controller.binaryMessenger)
        
        instantMeasurementChannel.setStreamHandler(InstantMeasurementStreamHandler())
        
        // 创建第六个通道，用于aoj事件
        let aojScanEventChannel = FlutterEventChannel(name: "com.aihnet.aicare_aoj_scan", binaryMessenger: controller.binaryMessenger)
        aojScanEventChannel.setStreamHandler(AojStreamHandler.shared)
        
        // 创建第七个通道，用于aoj事件
        let aojsingleChannel = FlutterMethodChannel(name: "com.aihnet.aicare_aoj", binaryMessenger: controller.binaryMessenger)
        
        aojsingleChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) in
            switch call.method {
                case "startScan":
                let filter = BTScanFilter()
                        filter.scanTypes = [
                            BTDeviceType.oximeter.rawValue,
                            BTDeviceType.thermometer.rawValue,
                            BTDeviceType.digitalThermometer.rawValue,
                            BTDeviceType.bloodPressureMeter.rawValue
                        ]
                AHDevicePlugin.default()?.searchDevice(filter, results: { device in
                              if let device = device {
                                  print("找到了设备" + device.macAddress!);
                                  AojStreamHandler.shared.pushDevice(device)
                              }
                          })
            
                        result(true)
            case "stopScan":
                      AHDevicePlugin.default()?.stopSearch()
                      result(true)
            case "addDevice":
                if let args = call.arguments as? [String: Any],
                   let macAddress = args["macAddress"] as? String,
                   let deviceTypeInt = args["deviceType"] as? Int {
                    let device = BTDeviceInfo.init()
                    device.macAddress = macAddress
                    // iOS 端通常也有 broadcastId 字段，优先用参数，否则用 macAddress
                    if let broadcastId = args["broadcastId"] as? String {
                        device.broadcastId = broadcastId
                    } else {
                        device.broadcastId = macAddress.replacingOccurrences(of: ":", with: "")
                    }
                    // 设备类型映射
                    switch deviceTypeInt {
                    case 0: device.deviceType = BTDeviceType.bloodPressureMeter
                    case 1: device.deviceType = BTDeviceType.digitalThermometer
                    case 2: device.deviceType = BTDeviceType.oximeter
                    case 3: device.deviceType = BTDeviceType.thermometer
                    default: device.deviceType = BTDeviceType.unknown
                    }
                    let success = AHDevicePlugin.default()?.addDevice(device) ?? false
                    if(success) {
                        self.aojDevice = device;
                    }
                    result(success)
                } else {
                    result(FlutterError(code: "INVALID_ARGUMENT", message: "Invalid argument", details: nil))
                }
            
            case "startSync":
                let status = AHDevicePlugin.default()?.managerStatus
                if status == .syncing {
                    result(FlutterError(code: "AOJ_SYNCING", message: "Already syncing", details: nil))
                } else if status == BTManagerState.scaning {
                    AHDevicePlugin.default()?.stopSearch()
                    // 启动同步
                    AHDevicePlugin.default()?.startAutoConnect(AojSyncDelegate.shared)
                    result(true)
                } else if status == BTManagerState.free {
                    AHDevicePlugin.default()?.startAutoConnect(AojSyncDelegate.shared)
                    result(true)
                } else {
                    result(FlutterError(code: "AOJ_BUSY", message: "SDK is busy with status: \(String(describing: status))", details: nil))
                }
            case "stopSync":
                if AHDevicePlugin.default()?.managerStatus == .syncing {
                    AHDevicePlugin.default()?.stopAutoConnect()
                    result(true)
                } else {
                    result(false)
                }
            case "pushSetting":
                if let args = call.arguments as? [String: Any],
                   let broadcastId = args["broadcastId"] as? String,
                   let settingType = args["settingType"] as? String,
                   let settingParams = args["params"] as? [String: Any]? {
                    
                    
                    
                    let setting: BTDeviceSetting
                    
                    switch settingType {
                    case "TempMode":
                        if let tempSetting = AHTempSetting(cmd: AHTempCmd.configMode) {
                            if let mode = settingParams?["mode"] as? Int,
                               let tempMode = AHTempMode(rawValue: UInt(mode)) {
                                tempSetting.mode = tempMode
                            } else {
                                tempSetting.mode = .adult // 默认成人
                            }
                            if let unit = settingParams?["unit"] as? Int {
                                tempSetting.unit = Int32(unit)
                            }
                            setting = tempSetting
                        } else {
                            result(FlutterError(code: "INIT_FAILED", message: "AHTempSetting init failed", details: nil))
                            return
                        }
                    case "startBpmMeasure":
                        let bpmSetting = AHBpmConfigSetting.init()
                        bpmSetting.config = AHBpmConfig.startMeasuring
                        setting = bpmSetting
                        
                    
                    default:
                        result(FlutterError(code: "INVALID_ARGUMENT", message: "Unknown setting type", details: nil))
                        return
                    }
                    // 推送设置
                    AHDevicePlugin.default()?.push(setting,toDevice: self.aojDevice) { state, error, data in
                        if state {
                            // 成功
                            result(true)
                        } else {
                            // 失败
//                            result(FlutterError(code: "PUSH_SETTING_FAILED",msg:"",call))
                            result(false)
                        }
                    }
                    result(true)
                } else {
                    result(FlutterError(code: "INVALID_ARGUMENT", message: "Invalid argument", details: nil))
                }
                  default:
                      result(FlutterMethodNotImplemented)
            }}

    
        
        
        
        
        
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    
    private func aizoInit(result : FlutterResult){
        self.service = BTService.shared()
        let uniqueId = KeychainHelper.shared.getUniqueID()
        service!.setUniqueAppIDKey(uniqueId)
        result(true)
    }
    
    private func aizoConnect(result: FlutterResult, deviceMac: String) {
        // 检查 service 是否为 nil
        guard let service = self.service else {
            print("service 未初始化");
            result(false)
            return
        }
        do {
            // 尝试绑定设备
            let isBound = try service.bindDevice(deviceMac)
            
            // 根据绑定结果返回
            result(true)
        } catch {
            // 处理绑定过程中出现的错误
            result(false)
        }
    }

    
    private func aizoInstantMeasurement(result : @escaping FlutterResult,cmd: DeviceCommand){
        guard let type = cmd.toMeasurementType() else { return }
        
        service!.measureReq.startMeasurement(with: type) { isStarted in
            if isStarted {
                print("\(cmd.rawValue)...")
            } else {
                result("测量未开始, 请重试!")
            }
        } acked: { isMeasuredSuccess in
            if !isMeasuredSuccess {
                result("测量失败, 请重试!")
            }
        } resultReceived: { data in
            let jsonStr = data.toJSONString()
            let test =  MyConvert.convertMeasureResultToDictionary(data)
            result(jsonStr)
            if  data.measurementResult == 0 || data.measurementResult == -1{
                result("测量失败, 请重试!")
            } else {
                result("测量成功!")
            }
        }
        
    }
    // Helper 函数：解析日期字符串为 DateComponents
    private func parseDateStringToComponents(dateString: String) -> DateComponents? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd" // 期望日期格式，例如 "2024-11-25"
        guard let date = dateFormatter.date(from: dateString) else {
            return nil
        }
        let calendar = Calendar.current
        return calendar.dateComponents([.year, .month, .day], from: date)
    }
    
    private func healthDataToJSON(with models: [BTHealthModel]) -> [[String: Any]] {
        var jsonArray: [[String: Any]] = []
        
        // 遍历模型并将每个模型的数据转换为字典，添加到 jsonArray 中
        for model in models {
            var jsonObject: [String: Any] = [:]
            jsonObject["spo2"] = model.spo2
            jsonObject["environmentTemperature"] = model.environmentTemperature
            jsonObject["dailyHeartRate"] = model.dailyHeartRate
            jsonObject["calories"] = model.calories
            jsonObject["timestamp"] = model.timestamp
            jsonObject["sosAlert"] = model.sosAlert
            jsonObject["date"] = model.date
            jsonObject["timeStr"] = model.timeStr
            jsonObject["distance"] = model.distance
            jsonObject["hrv"] = model.hrv
            jsonObject["bodyTemperature"] = model.bodyTemperature
            jsonObject["steps"] = model.steps
            
            // 将当前模型的字典添加到数组中
            jsonArray.append(jsonObject)
        }
        
        return jsonArray
    }
    
    private func fetchSleepData(for dateComponents: DateComponents, result: @escaping FlutterResult) {
        print("开始获取睡眠数据")

        // 先检查 service 是否为空
        guard let service = self.service else {
            print("service 为空")
            result(FlutterError(code: "SERVICE_NULL", message: "Service is null", details: nil))
            return
        }
        let isValid = service.sleepReq.fetch(with: dateComponents)
        print("查看isvalid")
        print(isValid)


        print("开始调用 handleData")
    
        service.sleepReq.handleData { [weak self] summary, details in
            print("进入了回调")
            
            if let summary = summary, let details = details {
                print("有数据")
                do {
                    print("开始转换格式")
                    let response = try self?.convertToAndroidFormat(summary: summary, details: details)
                    let jsonData = try JSONSerialization.data(withJSONObject: response as Any, options: [])
                    if let jsonString = String(data: jsonData, encoding: .utf8) {
                        print("转换成功")
                        result(jsonString)
                    }
                } catch {
                    print("转换失败: \(error)")
                    result(FlutterError(code: "DATA_CONVERSION",
                                      message: "数据转换失败: \(error.localizedDescription)",
                                      details: nil))
                }
            } else {
                print("暂无睡眠数据")
                result(nil)
            }
        }
    }

    private func convertToAndroidFormat(summary: BTSleepSummaryModel, details: [BTSleepDetailModel]) throws -> [String: Any] {
        // 时间处理（秒转毫秒）
        let dateMillis = summary.timestamp * 1000
        let startMillis = summary.startTimestamp * 1000
        let endMillis = summary.endTimestamp * 1000
        
        // 睡眠阶段转换
        let sleepDetails = details.map { detail -> [String: Any] in
            return [
                "time": detail.timestamp * 1000,
//                "endTime": detail.endTimestamp * 1000,
                "mode": convertSleepStage(detail.sleepMode),
//                "sleepStage": convertSleepStage(detail.sleepMode),
//                "duration": detail.duration
//                "duration": 1
            ]
        }
        
        // 构建与Android一致的结构
        return [
            "date": dateMillis,
            "startTime": startMillis,
            "endTime": endMillis,
            "totalDuration": summary.totalSleepDuration,
            "awakeDuration": summary.awakeDuration,
            "lightDuration": summary.lightSleepDuration,
            "deepDuration": summary.deepSleepDuration,
            "remDuration": summary.remSleepDuration,
            "details": sleepDetails
        ]
    }

    private func convertSleepStage(_ stage: SleepMode) -> Int {
        switch stage {
        case SleepMode.REM: return 5
        case SleepMode.awake:   return 3
        case SleepMode.deep:    return 2
        case SleepMode.light:   return 1
        case SleepMode.notWorn: return 4
        case SleepMode.shutdown: return -1
        default: return -1
        }
    }
    private func calculateHealthScore(_ call: FlutterMethodCall, result: @escaping FlutterResult)
    {
        guard let args = call.arguments as? [String: Any] else {
              result(FlutterError(code: "INVALID_ARGS", message: "参数格式错误", details: nil))
              return
            }
        // 1. 解析健康数据
        guard let healthDicts = args["healthData"] as? [[String: Any]] else {
            result(FlutterError(code: "MISSING_HEALTH_DATA", message: "缺少健康数据", details: nil))
            return
        }

        let healthData = healthDicts.compactMap { dict -> BTHealthModel? in
            guard let spo2 = dict["spo2"] as? Int,
                  let environmentTemperature = dict["environmentTemperature"] as? Double,
                  let dailyHeartRate = dict["dailyHeartRate"] as? Int,
                  let calories = dict["calories"] as? Double,
                  let timestamp = dict["timestamp"] as? Int,
                  let sosAlert = dict["sosAlert"] as? Bool,
                  let date = dict["date"] as? Int,
                  let timeStr = dict["timeStr"] as? String,
                  let distance = dict["distance"] as? Int,
                  let hrv = dict["hrv"] as? Int,
                  let bodyTemperature = dict["bodyTemperature"] as? Double,
                  let steps = dict["steps"] as? Int
            else {
                return nil
            }
            
            // 创建BTHealthModel实例
            let healthModel = BTHealthModel()
            healthModel.spo2 = UInt(spo2)
            healthModel.environmentTemperature = Float(environmentTemperature)
            healthModel.dailyHeartRate = UInt(dailyHeartRate)
            healthModel.calories = UInt(calories)
            healthModel.timestamp = UInt(timestamp)
            healthModel.sosAlert = sosAlert
            healthModel.date = UInt(date)
            healthModel.timeStr = timeStr
            healthModel.distance = UInt(distance)
            healthModel.hrv = UInt(hrv)
            healthModel.bodyTemperature = Float(bodyTemperature)
            healthModel.steps = UInt(steps)
            
            return healthModel
        }
        // 2. 解析睡眠数据
        guard let sleepSummaryDicts = args["sleepSummary"] as? [[String: Any]],
              let sleepDetailDicts = args["sleepDetails"] as? [[String: Any]] else {
            result(FlutterError(code: "MISSING_SLEEP_DATA", message: "缺少睡眠数据", details: nil))
            return
        }

        // 解析睡眠详情
        let sleepDetails = sleepDetailDicts.compactMap { dict -> BTSleepDetailModel? in
            guard let sleepMode = dict["sleepMode"] as? Int,
                  let timestamp = dict["timestamp"] as? Int else {
                return nil
            }
            
            let model = BTSleepDetailModel()
            model.sleepMode = convertIntToSleepMode(sleepMode)
            model.timestamp = UInt(timestamp)
            
            // 设置可选字段
            if let startTimestamp = dict["startTimestamp"] as? Int {
                model.startTimestamp = UInt(startTimestamp)
            }
            if let startTimeStr = dict["startTimeStr"] as? String {
                model.startTimeStr = startTimeStr
            }
            if let timestampStr = dict["timestampStr"] as? String {
                model.timestampStr = timestampStr
            }
            
            return model
        }
        // 解析睡眠总览
        let sleepSummary = sleepSummaryDicts.compactMap { dict -> BTSleepSummaryModel? in
            guard let startTimestamp = dict["startTimestamp"] as? Int,
                  let endTimestamp = dict["endTimestamp"] as? Int else {
                return nil
            }
            
            let model = BTSleepSummaryModel()
            
            // 必需字段
            model.startTimestamp = UInt(startTimestamp)
            model.endTimestamp = UInt(endTimestamp)
            
            // 其他字段
            if let measurementType = dict["measurementType"] as? Int {
                model.measurementType = UInt(measurementType)
            }
            
            if let awakeCount = dict["awakeCount"] as? Int {
                model.awakeCount = UInt(awakeCount)
            }
            
            if let totalSleepDuration = dict["totalSleepDuration"] as? Int {
                model.totalSleepDuration = UInt(totalSleepDuration)
            }
            
            if let startTimeStr = dict["startTimeStr"] as? String {
                model.startTimeStr = startTimeStr
            }
            
            if let endTimeStr = dict["endTimeStr"] as? String {
                model.endTimeStr = endTimeStr
            }
            
            if let notWornDuration = dict["notWornDuration"] as? Int {
                model.notWornDuration = UInt(notWornDuration)
            }
            
            if let timestamp = dict["timestamp"] as? Int {
                model.timestamp = UInt(timestamp)
            }
            
            if let awakeDuration = dict["awakeDuration"] as? Int {
                model.awakeDuration = UInt(awakeDuration)
            }
            
            if let remSleepDuration = dict["remSleepDuration"] as? Int {
                model.remSleepDuration = UInt(remSleepDuration)
            }
            
            if let lightSleepDuration = dict["lightSleepDuration"] as? Int {
                model.lightSleepDuration = UInt(lightSleepDuration)
            }
            
            if let deepSleepDuration = dict["deepSleepDuration"] as? Int {
                model.deepSleepDuration = UInt(deepSleepDuration)
            }
            
            // 初始化空的napDetails数组（按你的要求暂时设为空）
            model.napDetails = NSMutableArray()
            
            // 关联睡眠详情数据
            model.sleepDetails = sleepDetails
            
            return model
        }

        if sleepSummary.isEmpty {
            result(FlutterError(code: "EMPTY_SLEEP_SUMMARY", message: "睡眠总览数据为空", details: nil))
            return
        }
        // 4. 准备算法输入
        let timestamp = (healthData.first?.date ?? 0) * 1000
        let sleepDataArray = NSMutableArray(array: sleepSummary)
        let healthDataArray = NSMutableArray(array: healthData)
        let sportDataArray = NSMutableArray() // 空运动数据
        let healthScoreArray = NSMutableArray() // 空历史评分

        // 5. 执行计算
        let algorithm = BTScoreAlgorithm()
        algorithm.calculateHealthScore(
            withDate: timestamp,
            sleepData: sleepDataArray,
            healthData: healthDataArray,
            sportData: sportDataArray,
            healthScore: healthScoreArray
        ) { score in
            guard let score = score else {
                result(FlutterError(code: "CALCULATION_FAILED", message: "评分计算失败", details: nil))
                return
            }
            
            // 6. 转换结果为字典
            var resultDict = [String: Any]()
            resultDict["timestamp"] = score.timestamp
//            printContent(/*<#T##sender: Any?##Any?#>*/)
            print("你好32423423");
            print(score)
            print(score.sleepScore)
            print(score.activityScore)
            
//            NSLog("查看score")
//            NSLog("sleepScore: %@", score.sleepScore)
//            NSLog("activityScore: %@", score.activityScore)
//            NSLog("score: %@", score)
            
            // 睡眠评分
            if let sleepScore = score.sleepScore {
                resultDict["sleepScore"] = [
                    "timestamp": sleepScore.timestamp,
                    "sleepScore": sleepScore.sleepScore,
                    "sleepDurationScore": sleepScore.sleepDurationScore,
                    "sleepEfficiencyScore": sleepScore.sleepEfficiencyScore,
                    "deepSleepScore": sleepScore.deepSleepScore,
                    "remSleepScore": sleepScore.remSleepScore,
                    "restfulnessScore": sleepScore.restfulnessScore,
                    "sleepBreathScore": sleepScore.sleepBreathScore
                ]
            }
            
            // 活动评分
            if let activityScore = score.activityScore {
                resultDict["activityScore"] = [
                    "timestamp": activityScore.timestamp,
                    "activityScore": activityScore.activityScore,
                    "stayActivityScore": activityScore.stayActivityScore,
                    "meatDailyGoalScore": activityScore.meatDailyGoalScore,
                    "exerciseFrequencyScore": activityScore.exerciseFrequencyScore,
                    "exerciseVolumeScore": activityScore.exerciseVolumeScore
                ]
            }
            
            // 恢复评分
            if let readinessScore = score.readinessScore {
                resultDict["readinessScore"] = [
                    "timestamp": readinessScore.timestamp,
                    "readinessScore": readinessScore.readinessScore,
                    "previousSleepScore": readinessScore.previousSleepScore,
                    "previousActivityScore": readinessScore.previousActivityScore,
                    "recoveryIndexScore": readinessScore.recoveryIndexScore,
                    "temperatureScore": readinessScore.temperatureScore,
                    "rhrScore": readinessScore.rhrScore
                ]
            }
            
            result(resultDict)
        }
    }
    
    private func convertIntToSleepMode(_ mode: Int) -> SleepMode {
        switch mode {
        case 1:
            return SleepMode.light
        case 2:
            return SleepMode.deep
        case 3:
            return SleepMode.awake
        case 4:
            return SleepMode.notWorn
        case 5:
            return SleepMode.REM
        case 7:
            return SleepMode.shutdown
        default:
            return SleepMode.awake // 默认值
        }
    }

    




}


