import 'dart:async';

enum BluetoothTaskType {
  CONNECT,
  DISCONNECT,
  MEASURE_SPO2,
  MEASURE_HEART_RATE,
  MEASURE_TEMPERATURE,
  GET_BATTERY,
  GET_HARDWARE_INFO,
}

class BluetoothTask<T> {
  final String taskId;
  final BluetoothTaskType type;
  final DateTime createdAt;
  final Map<String, dynamic>? parameters;
  Completer<T>? completer;
  
  BluetoothTask({
    required this.type,
    this.parameters,
  }) : taskId = DateTime.now().millisecondsSinceEpoch.toString(),
       createdAt = DateTime.now();
}

class BluetoothTaskResult<T> {
  final String taskId;
  final T? data;
  final dynamic error;
  final bool isSuccess;
  
  BluetoothTaskResult.success(this.data) : 
    taskId = DateTime.now().millisecondsSinceEpoch.toString(),
    error = null,
    isSuccess = true;
    
  BluetoothTaskResult.error(this.error) :
    taskId = DateTime.now().millisecondsSinceEpoch.toString(),
    data = null,
    isSuccess = false;
}

class BluetoothError implements Exception {
  final String message;
  final String? code;
  
  BluetoothError(this.message, {this.code});
}

class DeviceDisconnectedError extends BluetoothError {
  DeviceDisconnectedError() : super('设备已断开连接', code: 'DEVICE_DISCONNECTED');
}

class MeasurementFailedError extends BluetoothError {
  MeasurementFailedError() : super('测量失败', code: 'MEASUREMENT_FAILED');
} 