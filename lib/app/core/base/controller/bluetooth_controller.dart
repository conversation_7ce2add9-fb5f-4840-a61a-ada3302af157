import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'dart:collection';
import 'package:aiCare/app/core/base/bluetooth/my_bluetooth_device.dart';
import 'package:aiCare/app/data/model/aizo_auto_data.dart';
import 'package:aiCare/app/data/model/aizo_auto_finial_data.dart';
import 'package:aiCare/app/data/model/aizo_sleep_data.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'package:aiCare/app/core/enum/aizo_battery_status.dart';
import 'package:aiCare/app/core/enum/aizo_connect_status.dart';
import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:aiCare/app/core/utils/logger_singleton.dart';
import 'package:aiCare/app/core/utils/permission_util.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/core/widget/bluetooth/bluetooth_dialog.dart';
import 'package:aiCare/app/data/model/aizo_headrt_rate.dart';
import 'package:aiCare/app/data/repository/bluetooth_repository_impl.dart';
import 'package:aiCare/app/core/base/bluetooth/bluetooth_task.dart';
import 'package:aiCare/app/modules/home/<USER>/home_controller.dart';
import 'package:aiCare/app/routes/app_pages.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:flutter/material.dart';
import 'package:string_validator/string_validator.dart';
import 'package:synchronized/synchronized.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:intl/intl.dart';

class BluetoothController extends GetxController {
  StreamSubscription<List<ScanResult>>? _scanResultsSubscription;
  final storage = SecureStorageService.instance;
  final Logger logger = LoggerSingleton.getInstance();
  final BluetoothRepositoryImpl bluetoothRepository = BluetoothRepositoryImpl();
  final DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();
  bool isIOSFirst = true;

  // 添加状态管理
  RxBool isConnected = false.obs;
  final RxBool _isUpdating = false.obs;
  final _updateLock = Lock();
  Timer? _measurementTimer;

  // 任务队列相关
  final Queue<BluetoothTask> _taskQueue = Queue();
  bool _isExecutingTask = false;
  BluetoothTask? _currentTask;
  final _taskResultController =
      StreamController<BluetoothTaskResult>.broadcast();

  // 设备状态相关
  bool _isBluetoothInitialized = false;
  static RxBool _isScanning = false.obs;
  static RxList<MyBluetoothDevice> _scanResults = <MyBluetoothDevice>[].obs;
  Rx<AizoConnectStatus> _aizoConnectStatus = AizoConnectStatus.DISCONNECTED.obs;
  Rx<int> _aizoBatteryLevel = 0.obs;
  Rx<AizoBatteryStatus> _aizoBatteryStatus = AizoBatteryStatus.NOT_CHARGING.obs;
  bool _isBluetoothRelatedPermissions = false;
  RxInt _aizoCurrentInterval = 20.obs;

  // 最近连接的设备
  Rx<MyBluetoothDevice?> _lastConnectedDevice = Rx<MyBluetoothDevice?>(null);

  // 初始化状态
  final Rx<bool> _isInitializing = false.obs;
  bool get isInitializing => _isInitializing.value;

  // AOJ设备相关状态
  RxBool _isAojConnected = false.obs;
  MyBluetoothDevice? _aojDevice;
  Rx<AizoConnectStatus> _aojConnectStatus = AizoConnectStatus.DISCONNECTED.obs;
  Rx<int> _aojBatteryLevel = 0.obs;
  Rx<AizoBatteryStatus> _aojBatteryStatus = AizoBatteryStatus.NOT_CHARGING.obs;
  Rx<double> _aojTemperature = 0.0.obs;
  Rx<String> _aojLastMeasurementTime = ''.obs;

  // AOJ设备相关变量
  BluetoothCharacteristic? _aojWriteCharacteristic;
  BluetoothCharacteristic? _aojNotifyCharacteristic;
  StreamSubscription<List<int>>? _aojNotifySubscription;

  // Getters
  bool get isBluetoothInitialized => _isBluetoothInitialized;
  bool get isScanning => _isScanning.value;
  List<MyBluetoothDevice> get scanResults => _scanResults;
  AizoConnectStatus get aizoConnectStatus => _aizoConnectStatus.value;
  int get aizoBatteryLevel => _aizoBatteryLevel.value;
  AizoBatteryStatus get aizoBatteryStatus => _aizoBatteryStatus.value;
  bool get isBluetoothRelatedPermissions => _isBluetoothRelatedPermissions;
  Stream<BluetoothTaskResult> get taskStream => _taskResultController.stream;
  MyBluetoothDevice? get lastConnectedDevice => _lastConnectedDevice.value;
  RxInt get aizoCurrentInterval => _aizoCurrentInterval;

  // AOJ设备相关的getters
  bool get isAojConnected => _isAojConnected.value;
  MyBluetoothDevice? get aojDevice => _aojDevice;
  AizoConnectStatus get aojConnectStatus => _aojConnectStatus.value;
  int get aojBatteryLevel => _aojBatteryLevel.value;
  AizoBatteryStatus get aojBatteryStatus => _aojBatteryStatus.value;
  double get aojTemperature => _aojTemperature.value;
  String get aojLastMeasurementTime => _aojLastMeasurementTime.value;

  // 添加标记变量
  RxBool _needsDataUpdate = false.obs;

  // 添加新的状态变量
  final RxBool _hasConnectedBefore = false.obs;
  bool get hasConnectedBefore => _hasConnectedBefore.value;

  MyBluetoothDevice? targetDevice;

  @override
  void onInit() {
    super.onInit();
    _setupTaskQueueListener();
    _loadLastConnectedDevice();
    isConnected.value = storage.getAizoConnectionStatus();
    logger.d("bluetooth 是否初始化: ${isConnected.value}");
    if (isConnected.value) {
      initialize();
    }
  }

  @override
  void onClose() {
    _measurementTimer?.cancel();
    _measurementTimer = null;
    _taskResultController.close();
    _scanResultsSubscription?.cancel();
    super.onClose();
  }

  /// 检查并请求所需权限
  Future<bool> _checkAndRequestPermissions() async {
    if (isBluetoothRelatedPermissions) return true;

    // 请求蓝牙权限
    final bluetoothGranted = await PermissionUtil.requestBluetooth();
    if (!bluetoothGranted) {
      logger.w('Bluetooth permission not granted');
      _isBluetoothRelatedPermissions = false;
      return false;
    }

    // 请求位置权限（用于蓝牙扫描）
    final locationGranted = await PermissionUtil.requestLocation();
    if (!locationGranted) {
      logger.w('Location permission not granted');
      _isBluetoothRelatedPermissions = false;
      return false;
    }

    // 请求通知权限
    final notificationGranted = await PermissionUtil.requestNotification();
    if (!notificationGranted) {
      logger.w('Notification permission not granted');
      _isBluetoothRelatedPermissions = false;
      return false;
    }

    _isBluetoothRelatedPermissions = true;
    logger.d("权限要求成功");
    return true;
  }

  /// 初始化蓝牙
  Future<bool> initialize() async {
    if (_isBluetoothInitialized) return true;
    if (_isInitializing.value) {
      // 等待初始化完成
      await _isInitializing.stream.firstWhere((value) => !value);
      return _isBluetoothInitialized;
    }

    _isInitializing.value = true;
    try {
      // 检查权限
      if (!await _checkAndRequestPermissions()) {
        // ToastUtil.showError(Get.context!, T.permissionRequest.tr);
        return false;
      }
      logger.d("权限检查成功");

      bluetoothRepository.init();

      // _aojListenScanResults();
      // _aojListenSyncData();
      // 监听蓝牙连接状态
      _aizoListenConnectionStatusTask();
      // 监听蓝牙电池状态
      _aizoListenBatteryStatusTask();

      // 获取存储的设备信息
      MyBluetoothDevice? device = storage.getAizoDevicesLast();
      // 如果已连接过，则尝试手动重连
      if (isConnected.value && device != null) {
        try {
          // 先进行扫描，确保设备状态完整
          logger.d('开始扫描以准备重连...');
          logger.d("查看重连设备: ${device.remoteId.str}");
          await scanDevicesTask(timeout: Duration(seconds: 3));

          // 等待一小段时间确保扫描完成
          await Future.delayed(Duration(seconds: 1));

          // 检查设备是否在扫描结果中
          bool deviceFound = false;
          MyBluetoothDevice? foundDevice;
          for (var scannedDevice in _scanResults) {
            if (scannedDevice.remoteId.str == device.remoteId.str) {
              deviceFound = true;
              foundDevice = scannedDevice; // 使用扫描到的设备实例
              break;
            }
          }

          if (deviceFound && foundDevice != null) {
            // 尝试重连
            logger.d('开始重连设备...');
            await _aizoConnectTask(foundDevice);
            logger.d('重连设备完成');
            // if (Platform.isAndroid)
            // updateMeasurementData();
            // aizoPostSleepData();
            ToastUtil.showSuccess(Get.context!, T.bluetoothReconnectSuccess.tr);
            //执行backend_util.dart中的获取健康数据并上
          } else {
            logger.d('未找到要重连的设备');
            // 重置连接状态
            isConnected.value = false;
            _aizoConnectStatus.value = AizoConnectStatus.DISCONNECTED;
            storage.setAizoConnectionStatus(false);
          }
        } catch (e) {
          logger.e('重连过程出错: $e');
          ToastUtil.showError(Get.context!, T.bluetoothConnectFailed.tr);
          // 重连失败时重置状态
          isConnected.value = false;
          _aizoConnectStatus.value = AizoConnectStatus.DISCONNECTED;
          storage.setAizoConnectionStatus(false);
        }
      }

      _isBluetoothInitialized = true;
      logger.i('Bluetooth initialized successfully');
      return true;
    } catch (e) {
      logger.e('Failed to initialize bluetooth: $e');
      ToastUtil.showError(Get.context!, T.bluetoothConnectFailed.tr);
      return false;
    } finally {
      _isInitializing.value = false;
    }
  }

  /// 设置任务队列监听器
  ///
  /// 该方法用于监听蓝牙任务队列的执行结果，主要功能：
  /// 1. 监听任务执行结果流
  /// 2. 当任务执行失败时，根据错误类型显示相应的错误提示
  /// 3. 处理不同类型的错误（设备断开、测量失败、蓝牙错误等）
  void _setupTaskQueueListener() {
    taskStream.listen((result) {
      if (!result.isSuccess) {
        _handleTaskError(result);
      }
    });
  }

  /// 处理任务执行错误
  ///
  /// 根据不同的错误类型显示对应的错误提示：
  /// - 设备断开连接：提示重新连接设备
  /// - 测量失败：提示重试测量
  /// - 蓝牙错误：显示具体错误信息
  /// - 其他错误：显示通用错误提示
  void _handleTaskError(BluetoothTaskResult result) {
    if (result.error is DeviceDisconnectedError) {
      ToastUtil.showError(Get.context!, T.deviceDisconnectedRetry.tr);
    } else if (result.error is MeasurementFailedError) {
      ToastUtil.showError(Get.context!, T.measurementFailedRetry.tr);
    } else if (result.error is BluetoothError) {
      ToastUtil.showError(Get.context!, result.error.message);
    } else {
      ToastUtil.showError(Get.context!, T.operationFailedRetry.tr);
    }
  }

  /// 添加任务到队列
  Future<T> _addTask<T>(BluetoothTask task) async {
    // 确保蓝牙已初始化
    if (!_isBluetoothInitialized) {
      final initialized = await initialize();
      if (!initialized) {
        throw BluetoothError('这里有问题蓝牙初始化失败');
      }
    }
    logger.d("查看剩余任务");
    logger.d(_taskQueue.length);
    logger.d(_taskQueue.first.toString());
    logger.d(_taskQueue.first.parameters);
    logger.d(_taskQueue.first.taskId);
    logger.d(_taskQueue.first.type);
    logger.d(_taskQueue.first.createdAt);
    


    Completer<T> completer = Completer<T>();
    task.completer = completer; 
    _taskQueue.add(task);
    _processNextTask();
    return completer.future;
  }

  /// 清空任务队列
  void _clearTaskQueue() {
    _taskQueue.clear();
    _isExecutingTask = false;
    _currentTask = null;
  }

  /// 处理下一个任务
  void _processNextTask() {
    if (_isExecutingTask || _taskQueue.isEmpty) return;

    _isExecutingTask = true;
    _currentTask = _taskQueue.removeFirst();

    _executeTask(_currentTask!).then((result) {
      _taskResultController.add(BluetoothTaskResult.success(result));
      _isExecutingTask = false;
      _currentTask = null;
      _processNextTask();
    }).catchError((error) {
      _taskResultController.add(BluetoothTaskResult.error(error));
      _isExecutingTask = false;
      _currentTask = null;
      _processNextTask();
    });
  }

  /// 执行任务
  Future<dynamic> _executeTask(BluetoothTask task) async {
    switch (task.type) {
      case BluetoothTaskType.CONNECT:
        return _aizoConnectTask(task.parameters?['device']);
      case BluetoothTaskType.MEASURE_SPO2:
        return _aizoMeasureSpO2Task(
            setMeasurement: task.parameters?['setMeasurement']);
      case BluetoothTaskType.MEASURE_HEART_RATE:
        return _aizoMeasureHeartRateTask(
            setMeasurement: task.parameters?['setMeasurement']);
      case BluetoothTaskType.MEASURE_TEMPERATURE:
        return _aizoMeasureTemperatureTask(
            setMeasurement: task.parameters?['setMeasurement']);
      case BluetoothTaskType.GET_HARDWARE_INFO:
        return _aizoGetHardwareInfoTask();
      default:
        throw BluetoothError('未知的任务类型');
    }
  }

  // ------ aizo 相关方法 ------

  // 连接设备
  Future<void> aizoConnect(MyBluetoothDevice device) async {
    try {
      ToastUtil.showInfo(Get.context!, T.bluetoothConnecting.tr);
      // 如果已经连接了其他设备，先断开
      if (isConnected.value) {
        await aizoDisconnect();
      }
      logger.d("开始lianjie1");

      await _addTask(
        BluetoothTask(
          type: BluetoothTaskType.CONNECT,
          parameters: {'device': device},
        ),
      );
      logger.d("完成任务");
    } catch (e) {
      logger.e("连接设备失败: $e");
      isConnected.value = false;
      _aizoConnectStatus.value = AizoConnectStatus.DISCONNECTED;
      storage.setAizoConnectionStatus(false);
      rethrow;
    }
  }

  // 设置测量间隔
  Future<bool> aizoSetMeasureInterval(int time) async {
    try {
      final bool result =
          await bluetoothRepository.aizoSetMeasureInterval(time);

      if (!result) {
        logger.e("设置心率间隔失败");
        return false;
      }

      logger.d("设置心率间隔成功");
      // 设置成功后更新定时器
      await _updateMeasurementTimer();
      return true;
    } on PlatformException catch (e) {
      logger.e("设置心率间隔失败: $e");
    } on Exception catch (e) {
      logger.e("设置心率间隔失败: $e");
    }

    return false;
  }

  // 设置测量定时器
  Future<void> setupMeasurementTimer() async {
    try {
      // 确保取消旧定时器
      _measurementTimer?.cancel();
      logger.d("取消旧定时器");
      logger.d("开始获取测量间隔");

      // 获取测量间隔
      final AizoHeartRate? heartRate =
          await bluetoothRepository.aizoGetMeasureInterval();
      if (heartRate == null || heartRate.currentInterval == 0) {
        logger.e("获取测量间隔失败");
        return;
      }

      logger.d("当前测量定时器，间隔: ${heartRate.currentInterval}分钟");
      _aizoCurrentInterval.value = heartRate.currentInterval;

      // 设置新定时器
      _measurementTimer = Timer.periodic(
        Duration(minutes: heartRate.currentInterval),
        (timer) => _handlePeriodicUpdate(),
      );

      // 立即执行一次更新
      // await _handlePeriodicUpdate();
    } catch (e) {
      logger.e("设置测量定时器失败: $e");
      rethrow;
    }
  }

  // 更新测量定时器
  Future<void> _updateMeasurementTimer() async {
    await setupMeasurementTimer();
  }

  // 更新测量数据
  Future<void> updateMeasurementData() async {
    try {
      logger.d("updateMeasurementData开始");
      //执行更新睡眠数据
      final result = await aizoPostSleepData();

      if (Get.isRegistered<HomeController>()) {
        final homeController = Get.find<HomeController>();
        logger.d("开始更新测量数据");
        await homeController.aizoAutoUpload();
        await homeController.fetchAllData();
      } else {
        logger.d("未注册homeController");
      }
      logger.d("数据更新完成");
    } catch (e) {
      logger.e("更新测量数据失败: $e");
    }
  }

  // 处理定时器回调
  Future<void> _handlePeriodicUpdate() async {
    if (!isConnected.value || _isUpdating.value) return;

    try {
      await _updateLock.synchronized(() async {
        _isUpdating.value = true;

        final isAppActive =
            WidgetsBinding.instance.lifecycleState == AppLifecycleState.resumed;

        if (isAppActive) {
          logger.d("应用在前台，开始更新数据");
          await updateMeasurementData();
        } else {
          logger.d("应用在后台，设置更新标志");
          storage.setBool(AppValues.isAutoUpdate, true);
          storage.setInt(AppValues.lastMeasurementKey,
              DateTime.now().millisecondsSinceEpoch);
        }
      });
    } catch (e) {
      logger.e("自动更新数据失败: $e");
    } finally {
      _isUpdating.value = false;
    }
  }

  // 检查并更新数据
  Future<void> checkAndUpdateData() async {
    if (!isConnected.value || _isUpdating.value) return;

    try {
      final needsUpdate = storage.getBool(AppValues.isAutoUpdate) ?? false;

      if (needsUpdate) {
        await _updateLock.synchronized(() async {
          _isUpdating.value = true;
          logger.d("应用回到前台，开始更新数据");
          await updateMeasurementData();
          storage.setBool(AppValues.isAutoUpdate, false);
          storage.setInt(AppValues.lastMeasurementKey,
              DateTime.now().millisecondsSinceEpoch);
        });
      }
    } catch (e) {
      logger.e("检查并更新数据失败: $e");
    } finally {
      _isUpdating.value = false;
    }
  }

  // 断开设备连接
  Future<void> aizoDisconnect() async {
    try {
      _measurementTimer?.cancel();
      _measurementTimer = null;
      isConnected.value = false;
      _isUpdating.value = false;

      await bluetoothRepository.aizoUnbind();
      _aizoConnectStatus.value = AizoConnectStatus.DISCONNECTED;
      isConnected.value = false;
      _lastConnectedDevice.value = null;
      // 清除更新标志
      storage.setBluetoothDataUpdate(false);
      storage.setAizoConnectionStatus(false);
      storage.deleteAizoDevicesLast();

      // if (Get.context != null) {
      //   ToastUtil.showSuccess(Get.context!, '设备已断开连接');
      // }
    } catch (e) {
      logger.e('断开连接失败: $e');
      if (Get.context != null) {
        ToastUtil.showError(Get.context!, T.disconnectFailedRetry.tr);
      }
      rethrow;
    }
  }

  // 执行血氧测量
  Future<Map<String, dynamic>> aizoMeasureSpO2({
    void Function(String value)? setMeasurement,
  }) =>
      _addTask(
        BluetoothTask(
            type: BluetoothTaskType.MEASURE_SPO2,
            parameters: {'setMeasurement': setMeasurement}),
      );

  // 执行心率测量
  Future<Map<String, dynamic>> aizoMeasureHeartRate({
    void Function(String value)? setMeasurement,
  }) =>
      _addTask(
        BluetoothTask(
            type: BluetoothTaskType.MEASURE_HEART_RATE,
            parameters: {'setMeasurement': setMeasurement}),
      );

  // 执行体温测量
  Future<Map<String, dynamic>> aizoMeasureTemperature({
    void Function(String value)? setMeasurement,
  }) =>
      _addTask(
        BluetoothTask(
            type: BluetoothTaskType.MEASURE_TEMPERATURE,
            parameters: {'setMeasurement': setMeasurement}),
      );

  // 获取电池电量
  Future<int> aizoGetBatteryLevel() => _addTask(
        BluetoothTask(type: BluetoothTaskType.GET_BATTERY),
      );

  // 获取硬件信息
  Future<Map<String, dynamic>> aizoGetHardwareInfo() => _addTask(
        BluetoothTask(type: BluetoothTaskType.GET_HARDWARE_INFO),
      );

  Future<AizoSleepData?> aizoPostSleepData() async {
    // 获取上次更新时间
    String? lastUpdateTimeString =
        await storage.getString(AppValues.aizoSleepLastUpdateTime);

    // 确定循环起始日期（转换为纯日期格式）
    DateTime now = DateTime.now();
    DateTime today = DateTime(now.year, now.month, now.day); // 今天0点
    DateTime start = today; // 默认从今天开始

    if (lastUpdateTimeString != null) {
      final cleanedString = lastUpdateTimeString.split(".")[0];
      DateTime lastUpdate = DateTime.parse(cleanedString);
      start =
          DateTime(lastUpdate.year, lastUpdate.month, lastUpdate.day); // 取日期部分
    }

    // 修复：确保结束日期包含今天
    DateTime endDate = today.add(const Duration(days: 1)); // 包含今天的结束日期

    logger.d("睡眠循环起始日期：$start, 结束日期：$endDate");

    AizoSleepData? lastResult;

    // 修复：使用正确的循环结束条件
    for (DateTime date = start;
        date.isBefore(endDate); // 使用 isBefore 而不是 isAfter
        date = date.add(const Duration(days: 1))) {
      logger.d("正在处理：$date");

      try {
        // 获取当天睡眠数据
        AizoSleepData? result =
            await bluetoothRepository.aizoGetSleepData(date);

        if (result != null) {
          // 上传有效数据
          await defaultRepositoryImpl.postSleepData(data: result);
        }

        // 如果是当前日期，存储最新数据
        if (date == today) {
          storage.setString(AppValues.aizoSleepLastUpdateTime, date.toString());
          lastResult = result;
          if (result != null) {
            storage.setString(
                AppValues.aizoSleepLast, json.encode(result.toJson()));
          }
        }
      } catch (e) {
        logger.e("处理 $date 数据时出错: $e");
      }
    }

    return lastResult;
  }

  // aizo 连接设备
  Future<void> _aizoConnectTask(MyBluetoothDevice device) async {
    try {
      logger.d("开始_aizoConnectTask");
      // 检查蓝牙是否开启
      var state = await FlutterBluePlus.adapterState.first;
      if (state == BluetoothAdapterState.off) {
        logger.d("蓝牙未开启");
        ToastUtil.showError(Get.context!, T.bluetoothOff.tr);
        return;
      }
      if (Platform.isAndroid) {
        logger.d('开始连接设备...21321231231');
        // 第一步：Flutter 端连接和绑定
        targetDevice = device;

        // 1. 监听连接状态
        var subscription = targetDevice?.connectionState
            .listen((BluetoothConnectionState state) async {
          if (state == BluetoothConnectionState.disconnected) {
            logger.d('设备断开连接');
            print(
                "${targetDevice?.disconnectReason?.code} ${targetDevice?.disconnectReason?.description}");
          }
          if (state == BluetoothConnectionState.connected) {
            logger.d('设备连接成功');
            print(
                "${targetDevice?.disconnectReason?.code} ${targetDevice?.disconnectReason?.description}");
          }
        });

        // 2. 开始 Flutter 端连接
        logger.d('开始 Flutter 端连接...');

        // 设置多轮重试
        int roundCount = 0;
        bool connectSuccess = false;

        while (!connectSuccess && roundCount < 5) {
          roundCount++;
          logger.d('开始第 $roundCount 轮连接尝试...');

          try {
            await targetDevice?.connect(autoConnect: true, mtu: null);

            // 每轮尝试10次
            int retryCount = 0;
            while (!connectSuccess && retryCount < 10) {
              connectSuccess = targetDevice?.isConnected ?? false;
              if (!connectSuccess) {
                await Future.delayed(Duration(seconds: 1));
                retryCount++;
                logger.d('第 $roundCount 轮 - 等待设备连接... 尝试次数: $retryCount');
              }
            }

            if (connectSuccess) {
              logger.d('第 $roundCount 轮连接成功');
              break;
            }

            // 如果当前轮次失败，等待2秒后开始下一轮
            if (!connectSuccess && roundCount < 5) {
              logger.d('第 $roundCount 轮连接失败，等待2秒后开始下一轮...');
              await Future.delayed(Duration(seconds: 2));
            }
          } catch (e) {
            logger.e('第 $roundCount 轮连接出错: $e');
            if (roundCount < 5) {
              await Future.delayed(Duration(seconds: 2));
            }
          }
        }

        if (!connectSuccess) {
          throw Exception('Flutter 端连接失败，已尝试5轮');
        }

        logger.d('Flutter 端连接成功，开始发现服务...');
        // 等待服务发现完成
        List<BluetoothService> services =
            await targetDevice?.discoverServices() ?? [];
        if (services.isEmpty) {
          throw Exception('服务发现失败');
        }
        logger.d('服务发现完成，发现 ${services.length} 个服务');

        // 4. 监听配对状态
        targetDevice?.bondState.listen((value) {
          if (value == BluetoothBondState.bonded) {
            logger.d('设备已配对: ${device.remoteId.str}');
          } else if (value == BluetoothBondState.bonding) {
            logger.d('配对中...');
          } else if (value == BluetoothBondState.none) {
            logger.d('未配对: ${device.remoteId.str}');
          } else {
            logger.d('配对状态: $value');
          }
        });

        // 5. 执行配对
        logger.d('开始配对...');
        await targetDevice?.createBond();
        await Future.delayed(Duration(seconds: 2));
      }

      // 第二步：厂商 SDK 连接
      logger.d('开始厂商 SDK 连接...');
      await bluetoothRepository.aizoConnect(
          device.remoteId.str, device.remoteId.str);

      await Future.delayed(Duration(seconds: 2));

      // 更新存储和状态
      _lastConnectedDevice.value = device;
      isConnected.value = true;
      _aizoConnectStatus.value = AizoConnectStatus.CONNECTED;
      storage.setAizoConnectionStatus(true);
      storage.setAizoDevicesLast(device);

      logger.d('设备连接和配对全部完成');

      logger.d("设置定时计了？连接设备成功");
      // 连接成功后设置测量定时器
      await setupMeasurementTimer();

      //执行更新健康数据和睡眠数据，并刷新页面展示数据
      updateMeasurementData();

      return;
    } on TimeoutException catch (e) {
      logger.e('连接超时: $e');
      rethrow;
    } on PlatformException catch (e) {
      logger.e('蓝牙平台错误: ${e.message}');
      rethrow;
    } catch (e) {
      logger.e('连接/配对失败: $e');

      // 错误处理：清理连接
      if (targetDevice != null) {
        try {
          // 断开 Flutter 端连接
          await targetDevice?.disconnect();
          if (defaultTargetPlatform == TargetPlatform.android) {
            await targetDevice?.removeBond();
          }
          // 断开厂商 SDK 连接
          await bluetoothRepository.aizoUnbind();
        } catch (e) {
          logger.e('清理操作失败: $e');
        }
      }

      // 重置状态
      isConnected.value = false;
      _aizoConnectStatus.value = AizoConnectStatus.DISCONNECTED;
      storage.setAizoConnectionStatus(false);
      _lastConnectedDevice.value = null;
      targetDevice = null;

      rethrow;
    }
  }

  // aizo 监听电池状态
  Future<void> _aizoListenBatteryStatusTask() async {
    logger.d("开始监听电池状态");
    bluetoothRepository.aizoBatteryStatus(
      setBatteryStatus: (status) {
        logger.d("电池状态status: $status");
        // 更新本地变量表明蓝牙电池状态
        _aizoBatteryStatus.value = AizoBatteryStatus.values.firstWhere(
          (e) => e.name == status[1],
          orElse: () => AizoBatteryStatus.NOT_CHARGING,
        );
        _aizoBatteryLevel.value = int.parse(status[0]);
      },
    );
  }

  // aizo 监听连接状态
  Future<void> _aizoListenConnectionStatusTask() async {
    bluetoothRepository.aizoRingStatus(
      onConnected: () {
        logger.d('onConnected连接的时候会执行状态监听设置吗？');
        // 更新蓝牙连接状态
        isConnected.value = true;
        _aizoConnectStatus.value = AizoConnectStatus.CONNECTED;
        storage.setAizoConnectionStatus(true);
      },
      onDisconnected: () {
        logger.d('onDisconnected连接的时候会执行状态监听设置吗？');
        // 断开连接时保持设备信息，但更新连接状态
        _aizoConnectStatus.value = AizoConnectStatus.DISCONNECTED;
        // 更新蓝牙连接状态
        isConnected.value = false;
        // 更新存储的连接状态
        storage.setAizoConnectionStatus(false);
      },
      setRingStatus: (status) {
        // 更新设备状态
      },
      updateConnectionStatus: (status) {
        // 更新连接状态
        _aizoConnectStatus.value = status
            ? AizoConnectStatus.CONNECTED
            : AizoConnectStatus.DISCONNECTED;
      },
    );
  }

  // aizo 测量血氧
  Future<Map<String, dynamic>> _aizoMeasureSpO2Task({
    void Function(String value)? setMeasurement,
  }) async {
    final callback = setMeasurement ?? (String value) {};
    logger.d("_aizoMeasureSpO2Task开始测量");
    if (Platform.isAndroid)
      bluetoothRepository.aizoInstantMeasurementAndroid(2, 1,
          setMeasurement: callback);
    else
      bluetoothRepository.aizoInstantMeasurementIOS(2, 1,
          setMeasurement: callback);
    return {};
  }

  // aizo 测量心率
  Future<Map<String, dynamic>> _aizoMeasureHeartRateTask({
    void Function(String value)? setMeasurement,
  }) async {
    final callback = setMeasurement ?? (String value) {};
    if (Platform.isAndroid)
      bluetoothRepository.aizoInstantMeasurementAndroid(1, 1,
          setMeasurement: callback);
    else
      bluetoothRepository.aizoInstantMeasurementIOS(1, 1,
          setMeasurement: callback);
    return {};
  }

  // aizo 测量体温
  Future<Map<String, dynamic>> _aizoMeasureTemperatureTask({
    void Function(String value)? setMeasurement,
  }) async {
    final callback = setMeasurement ?? (String value) {};
    if (Platform.isAndroid)
      bluetoothRepository.aizoInstantMeasurementAndroid(6, 1,
          setMeasurement: callback);
    else
      bluetoothRepository.aizoInstantMeasurementIOS(6, 1,
          setMeasurement: callback);
    return {};
  }

  // aizo 获取硬件信息
  Future<Map<String, dynamic>> _aizoGetHardwareInfoTask() async {
    bluetoothRepository.aizoGetHardwareData();
    return {};
  }

  /// aizo 加载最近连接的设备
  Future<void> _loadLastConnectedDevice() async {
    _lastConnectedDevice.value = storage.getAizoDevicesLast();
  }

  // ------ aoj 相关方法 ------
  /// 连接AOJ设备（通用）
  Future<void> aojConnect(MyBluetoothDevice device) async {
    try {
      if (_isAojConnected.value) {
        await aojDisconnect();
      }
      logger.d('开始连接AOJ设备: \\${device.remoteId.str}');
      _aojDevice = device;
      // 监听连接状态
      var subscription = _aojDevice?.connectionState
          .listen((BluetoothConnectionState state) async {
        if (state == BluetoothConnectionState.disconnected) {
          logger.d('AOJ设备断开连接');
          _isAojConnected.value = false;
          _aojConnectStatus.value = AizoConnectStatus.DISCONNECTED;
        }
        if (state == BluetoothConnectionState.connected) {
          logger.d('AOJ设备连接成功');
          _isAojConnected.value = true;
          _aojConnectStatus.value = AizoConnectStatus.CONNECTED;
        }
      });
      await _aojDevice?.connect(autoConnect: true, mtu: null);
      int retryCount = 0;
      while (!(_aojDevice?.isConnected ?? false) && retryCount < 10) {
        await Future.delayed(Duration(seconds: 1));
        retryCount++;
        logger.d('等待AOJ设备连接... 尝试次数: $retryCount');
      }
      if (!(_aojDevice?.isConnected ?? false)) {
        throw Exception('AOJ设备连接失败');
      }
      List<BluetoothService> services =
          await _aojDevice?.discoverServices() ?? [];
      if (services.isEmpty) {
        throw Exception('AOJ设备服务发现失败');
      }
      logger.d('AOJ设备服务发现完成，发现 \\${services.length} 个服务');
      // 查找服务和特征值（如有需要可扩展）
      for (var service in services) {
        logger.d('发现服务: \\${service.uuid}');
        for (var characteristic in service.characteristics) {
          logger.d('发现特征值: \\${characteristic.uuid}');
          // 这里只做保存，具体协议可后续扩展
          _aojWriteCharacteristic ??= characteristic;
          _aojNotifyCharacteristic ??= characteristic;
        }
      }
      // 开启通知（如有需要）
      if (_aojNotifyCharacteristic != null) {
        await _aojNotifyCharacteristic!.setNotifyValue(true);
        _aojNotifySubscription =
            _aojNotifyCharacteristic!.value.listen((value) {
          _handleAojResponse(value);
        });
      }
      _isAojConnected.value = true;
      _aojConnectStatus.value = AizoConnectStatus.CONNECTED;
      ToastUtil.showSuccess(Get.context!, T.aojDeviceConnectSuccess.tr);
    } catch (e) {
      logger.e('AOJ设备连接失败: $e');
      _isAojConnected.value = false;
      _aojConnectStatus.value = AizoConnectStatus.DISCONNECTED;
      ToastUtil.showError(Get.context!, T.aojDeviceConnectFailed.tr);
      rethrow;
    }
  }

  /// 断开AOJ设备（通用）
  Future<void> aojDisconnect() async {
    try {
      _aojNotifySubscription?.cancel();
      _aojNotifySubscription = null;
      if (_aojNotifyCharacteristic != null) {
        await _aojNotifyCharacteristic!.setNotifyValue(false);
      }
      if (_aojDevice != null) {
        await _aojDevice?.disconnect();
      }
      _isAojConnected.value = false;
      _aojConnectStatus.value = AizoConnectStatus.DISCONNECTED;
      _aojDevice = null;
      _aojWriteCharacteristic = null;
      _aojNotifyCharacteristic = null;
      ToastUtil.showSuccess(Get.context!, T.aojDeviceDisconnectSuccess.tr);
    } catch (e) {
      logger.e('断开AOJ设备失败: $e');
      ToastUtil.showError(Get.context!, T.aojDeviceDisconnectFailed.tr);
      rethrow;
    }
  }

  /// 处理AOJ设备响应数据（通用）
  void _handleAojResponse(List<int> value) {
    logger.d(
        '收到AOJ设备数据: \\${value.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');
    // 可根据协议扩展解析逻辑
  }

  /// 获取AOJ设备电池状态
  Future<int> aojGetBatteryLevel() async {
    try {
      if (!_isAojConnected.value) {
        throw Exception('AOJ设备未连接');
      }

      logger.d('获取AOJ设备电池状态...');

      // 这里需要根据AOJ设备的具体协议来实现电池状态获取
      // 暂时使用模拟数据
      await Future.delayed(Duration(seconds: 1));

      // 模拟电池电量（60-100%之间）
      int batteryLevel =
          60 + (DateTime.now().millisecondsSinceEpoch % 400) ~/ 10;
      _aojBatteryLevel.value = batteryLevel;

      logger.d('AOJ设备电池电量: $batteryLevel%');

      return batteryLevel;
    } catch (e) {
      logger.e('获取AOJ设备电池状态失败: $e');
      rethrow;
    }
  }

  /// 获取AOJ设备列表（只包含AOJ设备）
  List<MyBluetoothDevice> getAojDevices() {
    return _scanResults.where((device) {
      String deviceName = device.advName.toUpperCase();
      logger.d('getAojDevices:deviceName: $deviceName');
      // 根据AOJ设备的具体命名规则来过滤
      // 包含"AOJ"且包含"TEMP"、"THERMOMETER"或特定型号如"20A"
      return deviceName.contains('AOJ');
      // &&
      //     (deviceName.contains('TEMP') ||
      //         deviceName.contains('THERMOMETER') ||
      //         deviceName.contains('20A') || // AOJ-20A温度计
      //         deviceName.contains('THERMO')
      // ); // 其他可能的温度计关键词
    }).toList();
  }

  /// 检查设备是否为AOJ设备（非温度计）
  bool isAojNonThermometerDevice(MyBluetoothDevice device) {
    String deviceName = device.platformName.toUpperCase();
    return deviceName.contains('AOJ') &&
        !deviceName.contains('TEMP') &&
        !deviceName.contains('THERMOMETER') &&
        !deviceName.contains('20A') &&
        !deviceName.contains('THERMO');
  }

  // // aoj 监听扫描结果
  // void _aojListenScanResults() {
  //   // 监听AOJ扫描结果
  //   bluetoothRepository.aojScanResults.listen((deviceMap) {
  //     // 你可以自定义BluetoothDevice的构造方式
  //     logger.d("deviceMap: ${deviceMap}");

  //     final device = MyBluetoothDevice(
  //       DeviceIdentifier(deviceMap['macAddress'] ?? ''),
  //       deviceMap['name'] ?? '',
  //       // 你可以扩展platformName等字段
  //     );
  //     // 避免重复
  //     if (!_scanResults.any((d) => d.remoteId.str == device.remoteId.str)) {
  //       logger.d('添加AOJ扫描设备2312312: ${device.remoteId.str}');
  //       logger.d('添加AOJ扫描设备2312312: ${device.advName}');
  //       logger.d('添加AOJ扫描设备2312312: ${deviceMap['name']}');
  //       _scanResults.add(device);
  //     }
  //   });
  // }

  // // aoj 监听同步数据
  // void _aojListenSyncData() {
  //   bluetoothRepository.aojSyncStream.listen((data) {
  //     logger.d('收到AOJ同步数据: $data');
  //   });
  // }

  // //aoj 测量心率
  // aojMeasureBpm(MyBluetoothDevice device) {
  //   logger.d('bluetoothController: aojMeasureBpm: ${device.remoteId.str}');
  //   bluetoothRepository.aojMeasureBpm(device);
  // }

  // ------ 通用方法 ------

  /// 扫描蓝牙设备任务
  Future<void> scanDevicesTask({
    Duration timeout = const Duration(seconds: 5),
    List<Guid>? withServices,
  }) async {
    try {
      // 如果已经在扫描中，先停止当前扫描
      if (_isScanning.value) {
        await stopScan();
      }

      // 检查蓝牙是否开启
      var state = await FlutterBluePlus.adapterState.first;
      if (state == BluetoothAdapterState.off) {
        logger.d("蓝牙未开启");
        ToastUtil.showError(Get.context!, T.bluetoothOff.tr);
        return;
      }

      // 清空之前的扫描结果列表
      _scanResults.clear();

      if (Platform.isAndroid) {
        // 获取当前已连接的设备列表
        List<MyBluetoothDevice> connectedDevices = await FlutterBluePlus
            .connectedDevices
            .map((e) => MyBluetoothDevice(e.remoteId, e.platformName))
            .toList();

        logger.d('当前已连接设备数量: ${connectedDevices.length}');

        // 使用Map来存储设备，以MAC地址为key，确保设备不重复
        Map<String, MyBluetoothDevice> uniqueDevices = {};

        //添加已绑定设备列表

        // 遍历已连接的设备，筛选出名称包含AOJ或AIZO的设备
        for (var device in connectedDevices) {
          String deviceName = device.platformName.toUpperCase();
          logger.d('已连接设备: ${device.remoteId.str}, 名称: $deviceName');
          if (deviceName.contains('AIZO')) {
            uniqueDevices[device.remoteId.str] = device;
            logger.d('添加已连接设备到列表: ${device.remoteId.str}');
          }
        }

        // 获取已配对的设备
        List<MyBluetoothDevice> bondedDevices =
            (await FlutterBluePlus.bondedDevices)
                .map((e) => MyBluetoothDevice(e.remoteId, e.platformName))
                .toList();
        logger.d('已配对设备数量: ${bondedDevices.length}');

        // 添加已配对设备到列表
        for (var device in bondedDevices) {
          String deviceName = device.platformName.toUpperCase();
          logger.d('已配对设备: ${device.remoteId.str}, 名称: $deviceName');
          if (deviceName.contains('AIZO')) {
            uniqueDevices[device.remoteId.str] = device;
            logger.d('添加已配对设备到列表: ${device.remoteId.str}');
          }
        }

        // 开始新的蓝牙扫描
        logger.d('开始新的蓝牙扫描...');
        await FlutterBluePlus.startScan(
          timeout: timeout,
          withServices: withServices ?? [],
          androidUsesFineLocation: true, // 使用精确定位
          androidScanMode: AndroidScanMode.lowLatency, // 使用低延迟扫描模式
        );

        // 取消之前的扫描结果监听
        _scanResultsSubscription?.cancel();
        // 设置新的扫描结果监听
        _scanResultsSubscription =
            FlutterBluePlus.scanResults.listen((results) {
          // 处理新扫描到的设备
          for (var result in results) {
            String deviceName = result.device.platformName.toUpperCase();
            // 只保留名称包含AOJ或AIZO的设备
            if (deviceName.contains('AIZO')) {
              String remoteId = result.device.remoteId.str;
              // 如果设备不在Map中，则添加
              if (!uniqueDevices.containsKey(remoteId)) {
                uniqueDevices[remoteId] = MyBluetoothDevice(
                    result.device.remoteId, result.device.platformName);
                // logger.d('添加扫描设备到列表: $remoteId');
              }
            }
          }

          // 更新扫描结果列表
          _scanResults.clear();
          _scanResults.addAll(uniqueDevices.values);

          // logger.d('当前设备列表数量: ${_scanResults.length}');
          for (var device in _scanResults) {
            // logger.d('设备列表中的设备: ${device.remoteId.str}, 连接状态: ${device.isConnected}, 配对状态: ${device.bondState}');
          }
        });

        // 等待扫描完成
        await Future.delayed(timeout);

        // await aojScanSub.cancel();

        // 再次检查已连接和已配对设备
        connectedDevices = (await FlutterBluePlus.connectedDevices)
            .map((e) => MyBluetoothDevice(e.remoteId, e.platformName))
            .toList();
        bondedDevices = (await FlutterBluePlus.bondedDevices)
            .map((e) => MyBluetoothDevice(e.remoteId, e.platformName))
            .toList();

        // 合并所有设备
        for (var device in [...connectedDevices, ...bondedDevices]) {
          String deviceName = device.platformName.toUpperCase();
          if (deviceName.contains('AIZO') || deviceName.contains('AOJ')) {
            String remoteId = device.remoteId.str;
            if (!uniqueDevices.containsKey(remoteId)) {
              uniqueDevices[remoteId] = device;
              // logger.d('扫描结束后添加设备: $remoteId, 连接状态: ${device.isConnected}, 配对状态: ${device.bondState}');
            }
          }
        }

        // 最终更新扫描结果
        _scanResults.clear();
        _scanResults.addAll(uniqueDevices.values);
        // 1. 启动AOJ扫描
        // await bluetoothRepository.startAojScan();
        await Future.delayed(timeout);
        // await bluetoothRepository.stopAojScan();
        logger.d('最终设备列表数量: ${_scanResults.length}');
        for (var device in _scanResults) {
          logger.d(
              '最终设备列表中的设备: ${device.remoteId.str}, 连接状态: ${device.isConnected}, 配对状态: ${device.bondState.toString()}');
        }
      } else if (Platform.isIOS) {
        try {
          // 设置扫描状态
          _isScanning.value = true;
          final Map<String, MyBluetoothDevice> uniqueDevices = {};

          // 1. 获取已连接设备并立即添加到列表
          final connectedDevice = await bluetoothRepository.getAizoConnect();
          if (connectedDevice.macAddress != "") {
            final connectedBluetoothDevice = MyBluetoothDevice(
                DeviceIdentifier(connectedDevice.macAddress),
                connectedDevice.macAddress);
            uniqueDevices[connectedDevice.macAddress] =
                connectedBluetoothDevice;
            // 立即更新扫描结果列表
            _scanResults.clear();
            _scanResults.add(connectedBluetoothDevice);

            _lastConnectedDevice.value = connectedBluetoothDevice;
            // 更新存储和状态
            isConnected.value = true;
            _aizoConnectStatus.value = AizoConnectStatus.CONNECTED;
            // storage.setAizoConnectionStatus(true);
            // storage.setAizoDevicesLast(device);
            logger.d('立即添加已连接设备: ${connectedDevice.macAddress}');
          }

          // 2. 开始扫描
          final subscription = bluetoothRepository
              .startIOSScan((List<MyBluetoothDevice> devices) {
            logger.d("查看devices");
            logger.d(devices);
            // 2.1 保留已连接设备
            uniqueDevices.clear();
            if (connectedDevice.macAddress != "") {
              final connectedBluetoothDevice = MyBluetoothDevice(
                  DeviceIdentifier(connectedDevice.macAddress),
                  connectedDevice.macAddress);
              uniqueDevices[connectedDevice.macAddress] =
                  connectedBluetoothDevice;
            }

            // 2.2 添加扫描到的设备
            for (final device in devices) {
              if (device.remoteId.str.isNotEmpty) {
                uniqueDevices[device.remoteId.str] = device;
                logger.d('添加扫描设备: ${device.remoteId.str}');
              }
            }

            // 2.3 更新 _scanResults
            _scanResults.clear();
            _scanResults.addAll(uniqueDevices.values);

            // 2.4 输出日志
            logger.d('当前设备列表数量: ${_scanResults.length}');
            logger.d(
                '当前设备列表: ${_scanResults.map((e) => e.remoteId.str).toList()}');
          });

          // await bluetoothRepository.startAojScan();
          // 3. 等待扫描超时
          await Future.delayed(timeout);
          // await bluetoothRepository.stopAojScan();

          // 4. 取消扫描订阅
          subscription?.cancel();

          // 5. 设置扫描状态
          _isScanning.value = false;

          // 6. 最后再打印一次结果
          logger.d('扫描完成，最终设备数量: ${_scanResults.length}');
          logger.d('最终设备列表: ${_scanResults.map((e) => e.toString()).toList()}');
        } catch (e) {
          logger.e('iOS扫描过程出错: $e');
          _isScanning.value = false;
          rethrow;
        }
      }
    } catch (e) {
      logger.e('开始扫描失败: $e');
      rethrow;
    }
  }

  // 停止扫描
  Future<void> stopScan() async {
    try {
      await FlutterBluePlus.stopScan();
      _scanResultsSubscription?.cancel();
      _scanResultsSubscription = null;
    } catch (e) {
      print('停止扫描失败: $e');
      rethrow;
    }
  }

  // 显示蓝牙对话框
  showBluetoothDialog() async {
    await initialize();
    if (!isBluetoothInitialized) {
      logger.d("走的这里的");
      // ToastUtil.showError(Get.context!, T.bluetoothInitFailedRetry.tr);
      return;
    }

    // 点击蓝牙图标时弹出蓝牙设备选择弹窗
    Navigator.of(Get.context!).push(
      PageRouteBuilder(
        opaque: false,
        pageBuilder: (context, _, __) => BluetoothDialog(
          onSettingsTap: () async {
            bool? first = await storage.getBool(AppValues.bluetoothFirst);
            if (first == null) {
              storage.setBool(AppValues.bluetoothFirst, true);
              Get.toNamed(Routes.BLUETOOTH_FIRST);
            } else {
              Get.toNamed(Routes.BLUETOOTH_CONNECT);
            }
          },
        ),
      ),
    );
    //添加蓝牙扫描任务
    scanDevicesTask();
  }

  Future<AizoHeartRate?> aizoGetMeasureInterval() async {
    //获取值，返回间隔
    final AizoHeartRate? measureInterval =
        await bluetoothRepository.aizoGetMeasureInterval();
    return measureInterval;
  }

  Future<bool> aizoGetHealthScore() async {
    DateTime currentDay = DateTime(2025, 7, 31)
        .toUtc()
        .copyWith(hour: 0, minute: 0, second: 0, millisecond: 0);
    final result = await bluetoothRepository.aizoGetHealthScore(currentDay);
    logger.d("查看健康评分结果");
    logger.d(result);
    return true;
  }
}
