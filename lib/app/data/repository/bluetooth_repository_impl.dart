/*
 * @Author: 张仕鹏 <EMAIL>
 * @Date: 2024-10-16 14:45:59
 * @LastEditors: 张仕鹏 <EMAIL>
 * @LastEditTime: 2025-08-20 13:58:14
 * @FilePath: /RPM-APP-MASTER/lib/app/data/repository/bluetooth_repository_impl.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:intl/intl.dart';

import 'package:aiCare/app/core/base/bluetooth/my_bluetooth_device.dart';
import 'package:aiCare/app/core/base/controller/bluetooth_controller.dart';
import 'package:aiCare/app/core/enum/aizo_connect_status.dart';
import 'package:aiCare/app/core/translations/translation_keys.dart';
import 'package:aiCare/app/core/utils/toast_util.dart';
import 'package:aiCare/app/core/values/app_values.dart';
import 'package:aiCare/app/data/model/aizo_auto_data.dart';
import 'package:aiCare/app/data/model/aizo_auto_finial_data.dart';
import 'package:aiCare/app/data/model/aizo_headrt_rate.dart';
import 'package:aiCare/app/data/model/aizo_measure_result.dart';
import 'package:aiCare/app/data/model/aizo_ring.dart';
import 'package:aiCare/app/data/model/aizo_sleep_data.dart';
import 'package:aiCare/app/data/model/aizo_userinfo.dart';
import 'package:aiCare/app/data/model/bluetooth_response.dart';
import 'package:aiCare/app/data/repository/bluetooth_repository.dart';
import 'package:aiCare/app/core/base/remote/base_remote_source.dart';
import 'package:aiCare/app/data/repository/default_repository_impl.dart';
import 'package:aiCare/app/modules/blood_oxygen/model/oxygen_data.dart';
import 'package:aiCare/app/modules/bluetooth/model/aizo_dev_config.dart';
import 'package:aiCare/app/modules/heart_rate/model/heart_rate_data.dart';
import 'package:aiCare/app/modules/temperature/model/temperature_data.dart';
import 'package:aiCare/app/services/authorityService.dart';
import 'package:aiCare/app/core/service/storage_service.dart';
import 'package:aiCare/app/services/toastHelper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:string_validator/string_validator.dart';
// import 'dart:core';

class BluetoothRepositoryImpl extends BaseRemoteSource
    implements BluetoothRepository {
  DefaultRepositoryImpl defaultRepositoryImpl = DefaultRepositoryImpl();
  AuthorityService authorityservice = AuthorityService.instance;
  //定义蓝牙实例
  static FlutterBluePlus flutterBlue = FlutterBluePlus(); // 修改这里，不再使用 instance
  //定义 android 通信桥
  static const MethodChannel _channel =
      MethodChannel('com.aihnet.aicare_single_time');
  static const _batteryEventChannel =
      EventChannel("com.aihnet.aicare_manyTimesBatteryStatus");
  static const _scanningEventChannel =
      EventChannel("com.aihnet.aicare_manyTimesScanningStatus");

  static const _ringStatusEventChannel =
      EventChannel("com.aihnet.aicare_manyTimesRingStatus");
  EventChannel _instantMeasurementChannel =
      EventChannel('com.aihnet.aicare_manyTimesInstantMeasurement');

  // ================== AOJ 相关通道定义 ==================
  // static const _aojChannel = MethodChannel('com.aihnet.aicare_aoj');
  // static const _aojScanStream = EventChannel('com.aihnet.aicare_aoj_scan');
  // static const _aojSyncStream = EventChannel('com.aihnet.aicare_aoj_sync');

  bool debugging = true;
  //定义蓝牙状态
  bool isBlueOn = false;
  bool hasPermission = false;
  //蓝牙设备的列表
  List<MyBluetoothDevice> blueList = [];
  late StreamSubscription<List<ScanResult>> scanningSubscription;
  StreamController<BluetoothResponse> _controller =
      StreamController<BluetoothResponse>();
  // List<ScanResult> listAndroid = [];
  List<AizoRing> list = [];

  // SecureStorageService store = SecureStorageService.instance;

  @override
  Future<bool> init() async {
    try {
      logger.d("蓝牙初始化");
      final bool result = await _channel.invokeMethod("init");
      logger.d("成功初始化蓝牙设备:$result");
    } on Exception catch (e) {
      logger.d("蓝牙初始化报错了");
      logger.d(e);
      return false;
    }
    return true;
  }

  @override
  Future<void> aizoRingStatus(
      {required onConnected,
      required onDisconnected,
      required setRingStatus(String value),
      required updateConnectionStatus(bool value)}) async {
    try {
      logger.d("aizo监听设备状态");
      _ringStatusEventChannel.receiveBroadcastStream().listen(
        (dynamic result) {
          logger.d("设备状态变化：: $result");

          if (Platform.isAndroid) {
            _handleAndroidStatus(
                result, onConnected, onDisconnected, updateConnectionStatus);
          } else {
            _handleIOSStatus(result, onConnected, onDisconnected, setRingStatus,
                updateConnectionStatus);
          }
        },
        onError: (error) {
          logger.e("获取设备状态出错: $error");
        },
      );
    } on PlatformException catch (e) {
      logger.d("PlatformException报错了: $e");
    } on Exception catch (e) {
      logger.d("Exception报错了: $e");
    }
  }

  // 处理 Android 平台状态
  void _handleAndroidStatus(
    dynamic result,
    Function onConnected,
    Function onDisconnected,
    Function(bool) updateConnectionStatus,
  ) {
    if (result == AizoConnectStatus.CONNECTED.name) {
      logger.d("蓝牙连接成功");
      logger.d("Bluetooth connection successful.");
      ToastUtil.showSuccess(Get.context!, T.bluetoothConnected.tr);
      updateConnectionStatus(true);
      onConnected();
    } else if (result == AizoConnectStatus.CONNECTING.name) {
      logger.d("Bluetooth connection in progress...");
      ToastUtil.showSuccess(Get.context!, T.bluetoothConnecting.tr);
      updateConnectionStatus(false);
      onDisconnected();
    } else if (result == AizoConnectStatus.DISCONNECTED.name) {
      // logger.d("IOS 配对已删除，请指引用户去系统蓝牙模块进行取消蓝牙配对");
      // logger.d("The pairing has been deleted.");
      ToastUtil.showError(Get.context!, T.bluetoothDisConnected.tr);
      updateConnectionStatus(false);
      onDisconnected();
    } else {
      if (result == '1') {
        ToastUtil.showError(Get.context!, T.bluetoothOff.tr);
      } else {
        if (result == "1001") {
          ToastUtil.showError(Get.context!, T.bluetoothDisConnected.tr);
          updateConnectionStatus(false);
          onDisconnected();
        }
        // logger.d("蓝牙连接状态未知: $result");
        // ToastUtil.showError(Get.context!, "${T.bluetoothStatusUnknown.tr}: $result");
      }
    }
  }

  // 处理 iOS 平台状态
  void _handleIOSStatus(
    dynamic result,
    Function onConnected,
    Function onDisconnected,
    Function(String) setRingStatus,
    Function(bool) updateConnectionStatus,
  ) async {
    final status = result["data"];
    setRingStatus(status);

    switch (status) {
      case "PAIRING_REMOVED":
        logger.d("Ios 未进行取消系统配对，无法连接，请指引用户去系统蓝牙模块取消蓝牙配对");
        logger.d(
            "Unable to connect without canceling system pairing. Please go to the Bluetooth module of the system to cancel Bluetooth pairing.");
        ToastUtil.showError(Get.context!, T.bluetoothSystemPairing.tr);
        //延迟1s
        await Future.delayed(const Duration(seconds: 1));
        updateConnectionStatus(false);
        onDisconnected();
        break;

      case "CONNECTING":
        logger.d("Bluetooth connection in progress...");
        ToastUtil.showSuccess(Get.context!, T.bluetoothConnecting.tr);
        updateConnectionStatus(false);
        onDisconnected();
        break;

      case "NO_PAIR":
        logger.d("IOS 配对已删除，请指引用户去系统蓝牙模块进行取消蓝牙配对");
        logger.d(
            "The pairing has been deleted. Please go to the system Bluetooth module to cancel the Bluetooth pairing.");
        ToastUtil.showError(Get.context!, T.bluetoothPairingDelete.tr);
        updateConnectionStatus(false);
        onDisconnected();
        break;

      case "CONNECTED":
        logger.d("蓝牙连接成功");
        BluetoothController bluetoothController = Get.find();
        if (bluetoothController.isIOSFirst) {
          bluetoothController.isIOSFirst = false;
          bluetoothController.updateMeasurementData();
        }
        logger.d("Bluetooth connection successful.");
        ToastUtil.showSuccess(Get.context!, T.bluetoothConnected.tr);
        updateConnectionStatus(true);
        onConnected();
        break;

      case "CONNECT_FAILED":
        logger.d("蓝牙连接失败");
        logger.d("Bluetooth connection failed.");
        ToastUtil.showError(Get.context!, T.bluetoothConnectFailed.tr);
        updateConnectionStatus(false);
        onDisconnected();
        break;

      case "AUTH_REFUSED":
        logger.d("设备拒绝连接");
        logger.d("Device refused connection.");
        ToastUtil.showError(Get.context!, T.bluetoothAuthRefused.tr);
        updateConnectionStatus(false);
        onDisconnected();
        break;

      case "AUTH_SUCCESS":
        logger.d("设备认证成功");
        logger.d("Device authentication successful.");
        ToastUtil.showSuccess(Get.context!, T.bluetoothAuthSuccess.tr);
        updateConnectionStatus(true);
        onConnected();
        break;

      case "AUTH_BOUND":
        logger.d("设备已被其他手机绑定");
        logger.d("Device is already bound to another phone.");
        ToastUtil.showError(Get.context!, T.bluetoothAuthBound.tr);
        updateConnectionStatus(false);
        onDisconnected();
        break;

      case "AUTH_ILLEGAL":
        logger.d("非法厂商");
        logger.d("Illegal manufacturer.");
        ToastUtil.showError(Get.context!, T.bluetoothAuthIllegal.tr);
        updateConnectionStatus(false);
        onDisconnected();
        break;

      case "VENDOR_ID_DIFFERENT":
        logger.d("厂商ID不一致");
        logger.d("Vendor ID mismatch.");
        ToastUtil.showError(Get.context!, T.bluetoothVendorIdDifferent.tr);
        updateConnectionStatus(false);
        onDisconnected();
        break;

      case "APP_ID_DIFFERENT":
        logger.d("App的ID不一致");
        logger.d("App ID mismatch.");
        ToastUtil.showError(Get.context!, T.bluetoothAppIdDifferent.tr);
        updateConnectionStatus(false);
        onDisconnected();
        break;

      case "OFF":
        logger.d("蓝牙已关闭");
        logger.d("Device is off.");
        ToastUtil.showError(Get.context!, T.bluetoothOff.tr);
        updateConnectionStatus(false);
        onDisconnected();
        break;
      case "DISCONNECTED":
        logger.d("蓝牙取消链接");
        ToastUtil.showError(Get.context!, T.bluetoothDisConnected.tr);
        updateConnectionStatus(false);
        onDisconnected();

      default:
        logger.d("蓝牙连接状态未知: $status");
        ToastUtil.showError(
            Get.context!, "${T.bluetoothStatusUnknown.tr}: $status");
        break;
    }
  }

  @override
  Future<bool> aizoConnect(String deviceName, String deviceMac) async {
    try {
      logger.d("aizo蓝牙连接");
      print('macAddress: $deviceMac');

      final bool result = await _channel.invokeMethod("aizoConnect", {
        "deviceMac": deviceMac,
        "deviceName": deviceName,
      });

      logger.d("成功蓝牙连接:$result");
    } on PlatformException catch (e) {
    } on Exception catch (e) {
      logger.d("报错了");
      logger.d(e);
    }
    // TODO: implement init
    // throw UnimplementedError();
    return true;
  }

  @override
  Future<void> aizoBatteryStatus({
    required setBatteryStatus(List<String> value),
  }) async {
    try {
      _batteryEventChannel.receiveBroadcastStream().listen(
        (dynamic result) {
          logger.d("电池状态: $result");
          if (result is List) {
            // 确保每个元素都是字符串
            final List<String> batteryStatus = result.map((item) {
              if (item == null) return "0";
              return item.toString();
            }).toList();
            setBatteryStatus(batteryStatus);
          } else {
            logger.e("电池状态数据格式错误: $result");
            setBatteryStatus(["0", "UNKNOWN"]);
          }
        },
        onError: (error) {
          logger.e("获取电池状态出错: $error");
          setBatteryStatus(["0", "ERROR"]);
        },
      );
    } on PlatformException catch (e) {
      logger.d("PlatformException报错了: $e");
      setBatteryStatus(["0", "ERROR"]);
    } on Exception catch (e) {
      logger.d("Exception报错了: $e");
      setBatteryStatus(["0", "ERROR"]);
    }
  }

//IOS端需接入配置清单
//设备功能支持清单
  @override
  Future<AizoDevConfig?> aizoConfigurationList() async {
    try {
      if (Platform.isAndroid) {
        logger.d("安卓aizo蓝牙配置清单");
        final String? result =
            await _channel.invokeMethod("aizoConfigurationList");
        logger.d("成功获取蓝牙清单:$result");
        if (result == null) {
          logger.d("获取配置清单失败，返回 null");
          return null;
        }

        // 解析 JSON 字符串为 DeviceConfig 对象
        final Map<String, dynamic> jsonMap = json.decode(result);
        return AizoDevConfig.fromJson(jsonMap);
      } else if (Platform.isIOS) {
        logger.d("iOS蓝牙初始化");
        // iOS 端的处理...
      }
    } on PlatformException catch (e) {
      logger.e("获取配置清单失败: $e");
    } on Exception catch (e) {
      logger.e("获取配置清单失败: $e");
    }
    return null;
  }

  @override
  Future<bool> aizoDestory() async {
    try {
      if (Platform.isAndroid) {
        logger.d("安卓aizo销毁");
        // print('macAddress: $deviceMac');

        final bool result = await _channel.invokeMethod("aizoDestory");

        logger.d("成功安卓aizo销毁");
        return true;
      } else if (Platform.isIOS) {
        logger.d("iOS蓝牙销毁");
      }
    } on Exception catch (e) {
      logger.d("报错了");
      logger.d(e);
    }
    return false;
  }

  //ios修改到这里
  @override
  Future<List<int>?> aizoGetCurActGoal() async {
    try {
      if (Platform.isAndroid) {
        logger.d("安卓aizo获取当前活动目标");

        final List<dynamic>? result =
            await _channel.invokeMethod("aizoGetCurActGoal");

        if (result == null) {
          logger.d("获取活动目标失败，返回 null");
          return null;
        }

        // 将 dynamic 类型的列表转换为 int 类型的列表
        final List<int> activityGoals = result.map((e) => e as int).toList();

        logger.d("安卓aizo获取当前活动目标: $activityGoals");
        return activityGoals;
      } else if (Platform.isIOS) {
        logger.d("iOS获取当前活动目标");

        final List<dynamic>? result =
            await _channel.invokeMethod("aizoGetCurActGoal");

        if (result == null) {
          logger.d("获取活动目标失败，返回 null");
          return null;
        }

        // 将 dynamic 类型的列表转换为 int 类型的列表
        final List<int> activityGoals = result.map((e) => e as int).toList();

        logger.d("iOS获取当前活动目标: $activityGoals");
        return activityGoals;
      }
    } on PlatformException catch (e) {
      logger.e("获取活动目标失败: $e");
    } on Exception catch (e) {
      logger.e("获取活动目标失败: $e");
    }

    return null; // 发生错误时返回 null
  }

  @override
  Future<AizoUserInfo> aizoGetUserInfo() async {
    try {
      if (Platform.isAndroid) {
        logger.d("安卓aizo获取用户信息");
        // print('macAddress: $deviceMac');

        Map<Object?, dynamic> userInfoMap =
            await _channel.invokeMethod("aizoGetUserInfo");
        AizoUserInfo result = AizoUserInfo(
          birth: userInfoMap['birth'],
          gender: userInfoMap['gender'],
          height: userInfoMap['height'],
          weight: userInfoMap['weight'],
        );
        logger.d(result.toString());

        logger.d("成功安卓aizo获取用户信息");
        return result;
      } else if (Platform.isIOS) {
        logger.d("iOS蓝牙初始化");
      }
    } on PlatformException catch (e) {
    } on Exception catch (e) {
      logger.d("报错了");
      logger.d(e);
    }
    return AizoUserInfo();
  }

  @override
  Future<AizoUserInfo> aizoSetUserInfo(AizoUserInfo userInfo) async {
    try {
      if (Platform.isAndroid) {
        logger.d("安卓aizo设置用户信息");
        // print('macAddress: $deviceMac');

        Map<Object?, dynamic?> userInfoMap =
            await _channel.invokeMethod("aizoSetUserInfo", {
          "height": userInfo.height,
          "weight": userInfo.weight,
          "gender": userInfo.gender,
          "birth": userInfo.birth,
        });
        AizoUserInfo result = AizoUserInfo(
          birth: userInfoMap['birth'],
          gender: userInfoMap['gender'],
          height: userInfoMap['height'],
          weight: userInfoMap['weight'],
        );
        logger.d(result.toString());

        logger.d("成功安卓aizo设置用户信息");
        return result;
      } else if (Platform.isIOS) {
        logger.d("iOS蓝牙初始化");
      }
    } on PlatformException catch (e) {
    } on Exception catch (e) {
      logger.d("报错了");
      logger.d(e);
    }
    return AizoUserInfo();
  }

  @override
  Future<AizoHeartRate?> aizoGetMeasureInterval() async {
    try {
      final Map<dynamic, dynamic>? result =
          await _channel.invokeMethod("aizoGetMeasureInterval");

      if (result == null) {
        logger.d("获取心率间隔失败，返回 null");
        return null;
      }

      // 将 dynamic 类型的列表转换为 int 类型的列表
      final List<dynamic> rawIntervalList = result['intervalList'] ?? [];
      final List<int> intervalList =
          rawIntervalList.map((e) => e as int).toList();

      final AizoHeartRate heartRate = AizoHeartRate(
        currentInterval: result['currentInterval'] ?? 0,
        defaultInterval: result['defaultInterval'] ?? 0,
        intervalList: intervalList,
      );

      logger.d("成功获取心率间隔: $heartRate");
      return heartRate;
    } on PlatformException catch (e) {
      logger.e("获取心率间隔失败: $e");
    } on Exception catch (e) {
      logger.e("获取心率间隔失败: $e");
    }

    return null; // 发生错误时返回 null
  }

  @override
  Future<bool> aizoSetMeasureInterval(int time) async {
    try {
      final bool? result =
          await _channel.invokeMethod("aizoSetMeasureInterval", {
        "time": time,
      });

      if (result == null) {
        logger.e("设置心率间隔失败");
        return false;
      }

      logger.d("设置心率间隔结果: $result");
      return result;
    } on PlatformException catch (e) {
      logger.e("设置心率间隔失败: $e");
    } on Exception catch (e) {
      logger.e("设置心率间隔失败: $e");
    }

    return false;
  }

  @override
  Future<void> aizoInstantMeasurementAndroid(
    int type,
    int operation, {
    required setMeasurement(String value),
  }) async {
    try {
      // logger.d("安卓aizo开始测量");
      print("安卓开始测量");
      // print('macAddress: $deviceMac');

      Map<Object?, dynamic?> result =
          await _channel.invokeMethod("aizoInstantMeasurement", {
        "type": type,
        "operation": operation,
      });
      if (result['result'] == false) {
        setMeasurement("error: 测量失败");
        return;
      }

      if (result["result"] == true) {
        // logger.d("result['result']");
        // logger.d(result["result"]);
        switch (result["type"]) {
          case 2:
            logger.d("血氧结果");

            var newResult = OxygenData(
                dataSource: 0,
                percentage: result["bloodoxygen"].toInt(),
                date: DateTime.now());

            defaultRepositoryImpl.postOxygen(data: newResult);
            setMeasurement("successful: ${result['bloodoxygen']}");
            break;
          case 6:
            logger.d("体温结果");
            var newResult = TemperatureData(
                dataSource: 0, data: result["bodytemp"], date: DateTime.now());

            defaultRepositoryImpl.postTemperature(data: newResult);
            setMeasurement("successful: ${result['bodytemp']}");

            break;
          case 1:
            logger.d("心率结果");
            var newResult = HeartRateData(
                dataSource: 0, data: result["heartrate"], date: DateTime.now());

            defaultRepositoryImpl.postHeartRate(data: newResult);
            setMeasurement("successful: ${result['heartrate']}");

            break;
          default:
            logger.d("没对应选项");
        }
      }
      logger.d(result);
      // setMeasurement("successful: ${result.toString()}");
      // AizoMeasureResult aizoResult = AizoMeasureResult(
      //   result: result["result"],
      //   type: result["type"],
      //   time: result["time"],
      //   heartrate: result["heartrate"],
      //   bloodoxygen: result["bloodoxygen"],
      //   bodytemp: result["bodytemp"],
      //   envtemp: result["envtemp"],
      // );

      // logger.d(message)`

      // logger.d("成功测量");

      // setMeasurement("成功测量${result.toString()}");
      // return aizoResult;
    } on PlatformException catch (e) {
    } on Exception catch (e) {
      logger.d("报错了");
      logger.d(e);
    }
    // return AizoMeasureResult();
  }

  @override
  Future<void> aizoInstantMeasurementIOS(
    int type,
    int operation, {
    required setMeasurement(String value),
  }) async {
    try {
      logger.d("Flutter 调用: 开始测量");

      // 监听测量结果
      StreamSubscription<dynamic> subscription = _instantMeasurementChannel
          .receiveBroadcastStream(
              {'type': type, 'operation': operation}).listen(
        (dynamic result) async {
          logger.d("测量结果: $result");
          logger.d("result['event']：${result['event']}");
          if (result['event'] != 'result') {
            setMeasurement(result["message"]);
          }

          // 如果 completer 还未完成，使用首次结果完成它

          if (result['event'] == 'result') {
            switch (result["data"]["measureType"]) {
              case 1:
                logger.d("心率结果");
                logger.d(result["data"]);
                var newResult = HeartRateData(
                    dataSource: 2,
                    data: result["data"]["measurementResult"].toInt(),
                    date: DateTime.now());

                await defaultRepositoryImpl.postHeartRate(data: newResult);
                break;
              case 2:
                logger.d("血氧结果");

                var newResult = OxygenData(
                    dataSource: 2,
                    percentage: result["data"]["measurementResult"].toInt(),
                    date: DateTime.now());
                // logger.d("查看")

                await defaultRepositoryImpl.postOxygen(data: newResult);
                break;
              case 6:
                logger.d("体温结果");

                var newResult = TemperatureData(
                    dataSource: 0,
                    data: result["data"]["measurementResult"],
                    date: DateTime.now());

                await defaultRepositoryImpl.postTemperature(data: newResult);

                break;
              default:
                logger.d("没对应选项");
            }

            setMeasurement("successful: ${result['data']}");
          }
        },
        onError: (error) {
          logger.d("error: $error");

          setMeasurement("error: $error");
        },
      );
    } on PlatformException catch (e) {
      logger.d("PlatformException 出错: $e");
    } on Exception catch (e) {
      logger.d("Exception 出错: $e");
    }
  }

  @override
  Future<List<AizoAutoFinialData>> aizoGetHealthData(DateTime time) async {
    // 获取默认测量的数据列表
    var listResult = <AizoAutoFinialData>[];
    try {
      if (Platform.isIOS) {
        logger.d("iOS aizo 获取健康数据");

        // 将 DateTime 转换为 iOS 需要的字符串格式（yyyy-MM-dd）
        String formattedTime =
            "${time.year.toString().padLeft(4, '0')}-${time.month.toString().padLeft(2, '0')}-${time.day.toString().padLeft(2, '0')}";

        // 调用 iOS 方法并传递时间
        List<Object?> result =
            await _channel.invokeMethod("aizoGetHealthData", {
          "time": formattedTime,
        });
        logger.d("ios获取到的健康数据");
        logger.d(result);
        for (int i = 0; i < result.length; i++) {
          listResult.add(
              AizoAutoFinialData.fromJson(result[i] as Map<Object?, Object?>));
        }
      } else if (Platform.isAndroid) {
        logger.d("Android aizo 获取健康数据");
        // time = time.subtract(const Duration(days: 1));
        // logger.d("查看时间 ${time}");

        // 将 DateTime 转换为 iOS 需要的字符串格式（yyyy-MM-dd）
        String formattedTime =
            "${time.year.toString().padLeft(4, '0')}-${time.month.toString().padLeft(2, '0')}-${time.day.toString().padLeft(2, '0')}";
        logger.d("查看时间 ${formattedTime}");
        // 调用 iOS 方法并传递时间
        List<Object?> result =
            await _channel.invokeMethod("aizoGetHealthData", {
          "time": formattedTime.toDate()!.millisecondsSinceEpoch,
        });
        logger.d("Android获取到的健康数据");
        logger.d(result);
        for (int i = 0; i < result.length; i++) {
          listResult.add(AizoAutoFinialData.fromAdJson(
              result[i] as Map<Object?, Object?>));
        }
      }
    } on PlatformException catch (e) {
      logger.e("iOS 获取健康数据失败 (PlatformException): ${e.message}");
      logger.e(e.details);
    } on Exception catch (e) {
      logger.e("iOS 获取健康数据失败 (Exception): $e");
    }
    return listResult;
  }

  @override
  Future<bool> aizoUnbind() async {
    try {
      logger.d("aizo解绑");
      // print('macAddress: $deviceMac');

      bool result = await _channel.invokeMethod("aizoUnbind");

      logger.d("成功aizo解绑$result");
      // Get.snackbar('Success', 'Successfully unbound');
      return true;
    } on PlatformException catch (e) {
    } on Exception catch (e) {
      logger.d("报错了");
      logger.d(e);
      Get.snackbar('Error', 'Unbinding failed');
    }
    return false;
  }

  @override
  Future<AizoSleepData?> aizoGetSleepData(DateTime time) async {
    try {
      if (Platform.isAndroid) {
        logger.d("安卓aizo获取睡眠数据");
        // print('macAddress: $deviceMac');
        // time = time.subtract(const Duration(days: 1));
        logger.d("查看书面时间 ${time}");
        String formattedTime =
            "${time.year.toString().padLeft(4, '0')}-${time.month.toString().padLeft(2, '0')}-${time.day.toString().padLeft(2, '0')}";

        // 调用 iOS 方法并传递时间
        // List<Object?>
        dynamic response = await _channel.invokeMethod("aizoGetSleepData", {
          "time": DateTime.parse(formattedTime).millisecondsSinceEpoch,
        });

        // String result = await _channel.invokeMethod("aizoGetSleepData");
        // AizoMeasureResult aizoResult = AizoMeasureResult(
        //   result: result["result"],
        //   type: result["type"],
        //   time: result["time"],
        //   heartrate: result["heartrate"],
        //   bloodoxygen: result["bloodoxygen"],
        //   bodytemp: result["bodytemp"],
        //   envtemp: result["envtemp"],
        // );
        // logger.d(message)
        // logger.d(result);

        logger.d("成功安卓aizo获取睡眠信息");
        logger.d(response);

        logger.d(response.runtimeType);

        // 处理返回数据
        // if (response is List<dynamic>) {
        //   final List<AizoSleepData> sleepData = response
        //       .map((item) => AizoSleepData.fromJson(item as Map<String, dynamic>))
        //       .toList();

        //   logger.d("成功获取睡眠数据: ${sleepData.length}条");
        //   return sleepData;
        // } else {
        //   logger.w("无效数据格式: $response");
        //   return [];
        // }
        if (response != null) {
          try {
            final dynamic jsonData = json.decode(response); // 解析为动态类型

            // 关键：判断返回的是数组还是对象
            if (jsonData is List<dynamic>) {
              // 假设数组中只有一个睡眠记录（根据业务需求调整）
              if (jsonData.isNotEmpty) {
                final mapData = jsonData[0] as Map<String, dynamic>;
                return AizoSleepData.fromJsonToAPI(mapData);
              } else {
                logger.w("空数组，无睡眠数据");
              }
            } else if (jsonData is Map<String, dynamic>) {
              // 处理单个对象的情况（如果有）
              return AizoSleepData.fromJsonToAPI(jsonData);
            } else {
              logger.w("无效数据格式，期望数组或对象，实际类型：${jsonData.runtimeType}");
            }
          } catch (e) {
            logger.e("JSON 解析错误: $e");
          }
        }
      } else if (Platform.isIOS) {
        logger.d("iOS获取睡眠数据");
        String formattedTime =
            "${time.year.toString().padLeft(4, '0')}-${time.month.toString().padLeft(2, '0')}-${time.day.toString().padLeft(2, '0')}";

        dynamic response = await _channel.invokeMethod("aizoGetSleepData", {
          "time": DateTime.parse(formattedTime).millisecondsSinceEpoch,
        });
        logger.d("response:");
        logger.d(response);

        if (response != null && response is String) {
          try {
            final jsonData = json.decode(response);
            logger.d("jsonData123123: $jsonData");
            return AizoSleepData.fromJsonToAPI(jsonData);
          } catch (e) {
            logger.e("iOS JSON解析错误: $e");
          }
        } else {
          return null;
        }
      }
    } on PlatformException catch (e) {
    } on Exception catch (e) {
      logger.d("报错了");
      logger.d(e);
    }
    return null;
  }

  @override
  Future<String> aizoGetHardwareData() async {
    try {
      logger.d("aizo获取硬件信息");
      // print('macAddress: $deviceMac');

      String result = await _channel.invokeMethod("aizoGetHardwareData");
      // AizoMeasureResult aizoResult = AizoMeasureResult(
      //   result: result["result"],
      //   type: result["type"],
      //   time: result["time"],
      //   heartrate: result["heartrate"],
      //   bloodoxygen: result["bloodoxygen"],
      //   bodytemp: result["bodytemp"],
      //   envtemp: result["envtemp"],
      // );
      // logger.d(message)
      logger.d(result);

      logger.d("成功aizo获取硬件信息");
      return result;
    } on PlatformException catch (e) {
    } on Exception catch (e) {
      logger.d("报错了");
      logger.d(e);
    }
    return "失败";
  }

  @override
  Future<AizoRing> getAizoConnect() async {
    if (Platform.isAndroid) {
      final String? jsonString =
          await storage.getString(AppValues.androidDeviceID);

      if (jsonString == null || jsonString.isEmpty) {
        return AizoRing(); // 如果存储中没有数据，返回 null
      }
      final Map<String, dynamic> jsonData = jsonDecode(jsonString);
      return AizoRing.fromJson(jsonData); // 将 JSON 转换回 AizoRing 实例
    } else {
      // return await _channel.invokeMethod("getConnect");
      logger.d("ios获取aiRing已连接设备123");
      // logger.d();
      String result = await _channel.invokeMethod("getConnect");
      logger.d("ios返回结果：$result");
      if (result != "null") {
        return AizoRing(
            name: "aiRing", macAddress: result, isSystemConnected: true);
      } else {
        logger.d("ios没有已连接的aiRing设备123");
      }
    }
    return AizoRing();
  }

  @override
  StreamSubscription<dynamic>? startIOSScan(
      Function(List<MyBluetoothDevice>) onDevicesFound) {
    if (!Platform.isIOS) return null;
    return _scanningEventChannel.receiveBroadcastStream().listen(
      (dynamic result) {
        logger.d("iOS扫描结果: $result");
        // 期望 result 是 {"event": "aizoDeviceFound", "data": [设备列表]}
        if (result is Map &&
            result['event'] == 'aizoDeviceFound' &&
            result['data'] is List) {
          final List devicesData = result['data'];
          final List<MyBluetoothDevice> devices = devicesData.map((deviceMap) {
            return MyBluetoothDevice(
              DeviceIdentifier(deviceMap['macAddress'] ?? ''),
              deviceMap['name'] ?? '',
              // platformName: deviceMap['name'] ?? '',
              // 使用 localName 存储设备名称
              // localName: deviceMap['name'],
              // 使用 advName 存储广播名称
              // advName: deviceMap['name'],
              // rssi 信号强度
              // rssi: deviceMap['rssi'] ?? 0,
              // 其他可用信息
              // isConnected: deviceMap['isSystemConnected'] ?? false,
              // serviceUuids: [deviceMap['uuidString']]?.map((uuid) => Guid(uuid)).toList() ?? [],
            );
          }).toList();
          onDevicesFound(devices);
        }
      },
      onError: (error) {
        logger.e("iOS 扫描出错: $error");
      },
    );
  }

  // ================== AOJ 方法实现 ==================

  // @override
  // Future<bool> startAojScan() async {
  //   try {
  //     logger.d("开始扫描 AOJ 设备");
  //     final result = await _aojChannel.invokeMethod('startScan');
  //     logger.d("扫描启动结果: $result");
  //     return result == true;
  //   } on PlatformException catch (e) {
  //     logger.e("启动扫描失败: ${e.message}");
  //     return false;
  //   }
  // }

  // @override
  // Future<bool> stopAojScan() async {
  //   try {
  //     logger.d("停止扫描 AOJ 设备");
  //     final result = await _aojChannel.invokeMethod('stopScan');
  //     logger.d("停止扫描结果: $result");
  //     return result == true;
  //   } on PlatformException catch (e) {
  //     logger.e("停止扫描失败: ${e.message}");
  //     return false;
  //   }
  // }

  // @override
  // Future<bool> addAojDevice(String macAddress, int deviceType) async {
  //   try {
  //     logger.d("添加 AOJ 设备: $macAddress ($deviceType)");
  //     final result = await _aojChannel.invokeMethod('addDevice', {
  //       'macAddress': macAddress,
  //       'deviceType': deviceType,
  //     });
  //     logger.d("添加设备结果: $result");
  //     return result;
  //   } on PlatformException catch (e) {
  //     logger.e("添加设备失败: ${e.message}");
  //     return false;
  //   }
  // }

  // @override
  // Future<bool> startAojSync() async {
  //   try {
  //     logger.d("开始 AOJ 数据同步");
  //     final result = await _aojChannel.invokeMethod('startSync');
  //     logger.d("开始同步结果: $result");
  //     return result == true;
  //   } on PlatformException catch (e) {
  //     logger.e("开始同步失败: ${e.message}");
  //     return false;
  //   }
  // }

  // @override
  // Future<bool> stopAojSync() async {
  //   try {
  //     logger.d("停止 AOJ 数据同步");
  //     final result = await _aojChannel.invokeMethod('stopSync');
  //     logger.d("停止同步结果: $result");
  //     return result == true;
  //   } on PlatformException catch (e) {
  //     logger.e("停止同步失败: ${e.message}");
  //     return false;
  //   }
  // }

  // @override
  // Future<bool> setTempMode(String deviceId, int mode, int unit) async {
  //   try {
  //     logger.d("设置温度计模式: 设备 $deviceId, 模式 $mode, 单位 $unit");
  //     final result = await _aojChannel.invokeMethod('pushSetting', {
  //       'broadcastId': deviceId,
  //       'settingType': 'TempMode',
  //       'params': {
  //         'mode': mode,
  //         'unit': unit,
  //       }
  //     });
  //     logger.d("设置结果: $result");
  //     return result == true;
  //   } on PlatformException catch (e) {
  //     logger.e("设置温度计模式失败: ${e.message}");
  //     return false;
  //   }
  // }

  // @override
  // Future<bool> setBpmUser(String deviceId, int user) async {
  //   try {
  //     logger.d("设置血压计用户: 设备 $deviceId, 用户 $user");
  //     final result = await _aojChannel.invokeMethod('pushSetting', {
  //       'broadcastId': deviceId,
  //       'settingType': 'BpmUserSwitch',
  //       'params': {
  //         'user': user,
  //       }
  //     });
  //     logger.d("设置结果: $result");
  //     return result == true;
  //   } on PlatformException catch (e) {
  //     logger.e("设置血压计用户失败: ${e.message}");
  //     return false;
  //   }
  // }

  // @override
  // Stream<Map<dynamic, dynamic>> get aojScanResults {
  //   return _aojScanStream.receiveBroadcastStream().map((data) {
  //     logger.d("发现 AOJ 设备: $data");
  //     return data as Map<dynamic, dynamic>;
  //   });
  // }

  // @override
  // Stream<Map<dynamic, dynamic>> get aojSyncStream {
  //   return _aojSyncStream.receiveBroadcastStream().map((data) {
  //     logger.d("AOJ 同步数据: $data");
  //     return data as Map<dynamic, dynamic>;
  //   });
  // }

  // @override
  // Future<bool> aojMeasureBpm(MyBluetoothDevice device) async {
  //   logger.d('bluetoothRepositoryImpl: aojMeasureBpm: ${device.remoteId.str}');
  //   await _aojChannel.invokeMethod('pushSetting', {
  //     'macAddress': device.remoteId.str,
  //     'settingType': 'startBpmMeasure',
  //     'settingParams':"",
  //     'broadcastId': device.remoteId.str,
  //   });
  //   return true;
  // }

  @override
  Stream<Map<dynamic, dynamic>> get aojSyncData => throw UnimplementedError();

  @override
  Future<String> aizoGetHealthScore(DateTime time) async {
    //1.获取当前日期测量数据列表
    var healthResult = await defaultRepositoryImpl.getAutoData(time);
    List<AizoAutoData> healthList = [];

    if (healthResult != null) {
      if (healthResult is List<AizoAutoData>) {
        logger.d("查看healthResult");
        logger.d("成功获取到 ${healthResult.length} 条健康数据");
        healthList = healthResult;

        // 可选：打印第一条数据作为示例
        if (healthList.isNotEmpty) {
          // logger.d("第一条数据: ${healthList.first.toString()}");
          logger.d(healthList);
        }
      } else {
        logger.w(
            "healthResult 类型不匹配，期望 List<AizoAutoData>，实际: ${healthResult.runtimeType}");
      }
    } else {
      logger.d("healthResult 为空，当前时间无健康数据");
    }
    //2.获取当前日期睡眠数据
    // 使用统一的时间格式

    var fromDate = time.subtract(Duration(days: 1));
    // logger.d("起始时间：${fromDate.toIso8601String()}");
    // logger.d("结束时间: ${time.toString()}");
    String fromDateStr =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(fromDate.toUtc());
    String toDateStr =
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(time.toUtc());
    logger.d(fromDateStr);
    logger.d(toDateStr);
    var sleepResult =
        await defaultRepositoryImpl.getSleepDataList(fromDateStr, toDateStr);
    logger.d("查看获取到的睡眠数据");
    logger.d(sleepResult);

    //3.从当前日期睡眠数据得到睡眠详情
    String result = await _channel.invokeMethod("calculateHealthScore", {
      "healthData": healthList,
      "sleepSummary": sleepResult[0],
      "sleepDetail": sleepResult[0].details,
    });
    logger.d("获取到的健康频分结果");
    logger.d(result);
    return "nihao";
  }
}
